import React, { useState, useEffect } from "react";
import { FaSearch, FaTimes } from "react-icons/fa";
import axios from "axios";

const baseURL = import.meta.env.VITE_BASE_API || "http://127.0.0.1:8000";

const TYPE_OPTIONS = [
  { value: "all", label: "All" },
  { value: "Community Member", label: "Community Member" },
  { value: "Organization Member", label: "Organization Member" },
  { value: "Company", label: "Company" },
];

const MemberSearchAutocomplete = ({
  value = "",
  memberId = "",
  onSelect,
  unitId = null,
  isOwnerSearch = false,
  disabled = false,
  readOnly = false,
  isDisabled = false,
  hideClearButton = false
}) => {
  const [searchTerm, setSearchTerm] = useState(value);
  const [members, setMembers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [selectedMember, setSelectedMember] = useState(
    memberId && value ? { id: memberId, full_name: value } : null
  );
  const [selectedType, setSelectedType] = useState("all");

  // Update input from parent on mount or prop change
  useEffect(() => {
    setSearchTerm(value);
    if (memberId && value) {
      setSelectedMember({ id: memberId, full_name: value });
    }
  }, [value, memberId]);

  useEffect(() => {
    if (searchTerm.trim().length < 3 || selectedMember || disabled || isDisabled) {
      setMembers([]);
      setError("");
      return;
    }

    const fetchMembers = async () => {
      setLoading(true);
      setError("");
      try {
        let url = `${baseURL}/towers/add_owner_search/?search=${encodeURIComponent(searchTerm)}`;
        if (isOwnerSearch && unitId) {
          url += `&unit_id=${unitId}`;
        }
        const res = await axios.get(url);
        
        // Debug: Log the raw response data
        console.log("🔍 Raw API response:", res.data);
        
        // Group members by ID and combine their roles and locations
        const groupedMembers = res.data?.member_data?.reduce((acc, current) => {
          const existingMember = acc.find(item => item.id === current.id);
          if (!existingMember) {
            // Store locations as array of {tower, unit, role}
            return acc.concat([{
              ...current,
              locations: [{
                tower: current.tower,
                unit: current.unit,
                role: Array.isArray(current.roles) ? current.roles[0] : current.roles
              }]
            }]);
          } else {
            // Add new location with its specific role
            const alreadyExists = existingMember.locations.some(loc =>
              loc.tower === current.tower && loc.unit === current.unit && loc.role === (Array.isArray(current.roles) ? current.roles[0] : current.roles)
            );
            if (!alreadyExists) {
              existingMember.locations.push({
                tower: current.tower,
                unit: current.unit,
                role: Array.isArray(current.roles) ? current.roles[0] : current.roles
              });
            }
            return acc;
          }
        }, []) || [];
        
        // Debug: Log the grouped members
        console.log("🔍 Grouped members:", groupedMembers);
        
        setMembers(groupedMembers);
      } catch (err) {
        console.error("Failed to fetch members:", err);
        setMembers([]);
        setError("Failed to load members. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    const delay = setTimeout(fetchMembers, 300);
    return () => clearTimeout(delay);
  }, [searchTerm, selectedMember, isOwnerSearch, unitId, disabled, isDisabled]);

  const handleSelect = (member) => {
    if (disabled || isDisabled) return;
    setSelectedMember(member);
    setSearchTerm(member.full_name);
    setMembers([]);
    if (onSelect) onSelect(member);
  };

  const handleClear = () => {
    if (disabled || isDisabled) return;
    setSelectedMember(null);
    setSearchTerm("");
    setMembers([]);
    if (onSelect) onSelect({ id: "", full_name: "" });
  };

  // Helper function to format locations
  const formatLocations = (member) => {
    if (member.locations && member.locations.length > 0) {
      return member.locations.map(loc => 
        `${loc.tower || '—'} / ${loc.unit || '—'}`
      ).join(', ');
    }
    return `${member.tower || '—'} / ${member.unit || '—'}`;
  };

  // Filtered members by selectedType
  const filteredMembers = members.map(member => {
    // Check if this member is a company and also an owner
    const isCompany = member.locations.some(l => l.role === "Company");
    const isOwner = member.locations.some(l => l.role === "Owner");
    const ownerLocation = isOwner ? member.locations.find(l => l.role === "Owner") : null;

    return {
      ...member,
      locations: member.locations.filter(loc => {
        // If isOwnerSearch is true, only show owners
        if (isOwnerSearch) {
          return loc.role === "Owner";
        }
        
        // Otherwise, apply the selected type filter
        if (selectedType === "all") {
          // For companies that are owners, only show their owner role
          if (isCompany && isOwner) {
            return loc.role === "Owner";
          }
          // For other cases, show all except companies
          return !isCompany;
        }
        
        if (selectedType === "Community Member") {
          // For Community Member filter, show only non-company owners
          return (
            (loc.role === "Owner" && !isCompany) ||
            loc.role === "Resident" ||
            loc.role === "Unit Staff"
          );
        }
        
        if (selectedType === "Organization Member") {
          return loc.role === "Management";
        }
        
        if (selectedType === "Company") {
          // For companies that are owners, show owner location
          if (isCompany && isOwner) {
            return loc.role === "Owner";
          }
          // For regular companies, show company location
          return isCompany;
        }
        
        return false;
      }).map(loc => {
        // If in Company filter and this is a company-owner, use owner details
        if (selectedType === "Company" && isCompany && isOwner) {
          return {
            ...loc,
            tower: ownerLocation?.tower || loc.tower,
            unit: ownerLocation?.unit || loc.unit,
            role: "Owner"
          };
        }
        return loc;
      })
    };
  }).filter(member => member.locations.length > 0);

  // Debug: Log filtering results
  console.log("🔍 Selected type:", selectedType);
  console.log("🔍 All members before filtering:", members);
  console.log("🔍 Filtered members:", filteredMembers);

  return (
    <div className="bg-white border shadow rounded-xl p-4 relative">
      <div className="relative mb-4">
        <span className="absolute inset-y-0 left-0 flex items-center pl-3">
          <FaSearch className={`${disabled || isDisabled ? 'text-gray-400' : 'text-gray-500'}`} />
        </span>
        <input
          type="text"
          placeholder="Search members..."
          value={searchTerm}
          onChange={e => {
            if (disabled || isDisabled) return;
            setSearchTerm(e.target.value);
            setSelectedMember(null);
          }}
          className={`w-full pl-10 border border-gray-300 rounded-lg p-2 focus:outline-none ${disabled || isDisabled ? 'bg-gray-100 cursor-not-allowed' : ''
            }`}
          disabled={disabled || isDisabled || !!selectedMember}
          readOnly={readOnly}
        />
        {selectedMember && !disabled && !isDisabled && !hideClearButton && (
          <button
            type="button"
            className="absolute right-2 top-2 text-gray-400 hover:text-red-500"
            onClick={handleClear}
            title="Clear selection"
          >
            <FaTimes />
          </button>
        )}
      </div>

      {searchTerm.length >= 3 && !selectedMember && !disabled && !isDisabled && (
        <>
          {/* Radio button filter options - only show when not searching for owners */}
          {!isOwnerSearch && (
            <div className="flex flex-wrap gap-4 mb-4 items-center">
              {TYPE_OPTIONS.map(option => (
                <label key={option.value} className="flex items-center cursor-pointer">
                  <input
                    type="radio"
                    name="member-type-filter"
                    value={option.value}
                    checked={selectedType === option.value}
                    onChange={() => setSelectedType(option.value)}
                    className="accent-teal-500 mr-2"
                  />
                  <span className={selectedType === option.value ? "text-teal-500 font-semibold" : "text-gray-700"}>{option.label}</span>
                </label>
              ))}
            </div>
          )}
          <h2 className="text-lg font-semibold mb-2">
            {isOwnerSearch ? "Select Unit Owner" : "Select Member"}
          </h2>
          <div className="overflow-x-auto max-h-60 overflow-y-auto border rounded">
            <table className="w-full table-auto border-collapse text-sm">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="px-4 py-2 text-left">Name</th>
                  <th className="px-4 py-2 text-left">Type</th>
                  <th className="px-4 py-2 text-left">Tower</th>
                  <th className="px-4 py-2 text-left">Unit</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="4" className="text-center py-4">
                      Loading...
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan="4" className="text-center py-4 text-red-500">
                      {error}
                    </td>
                  </tr>
                ) : filteredMembers.length === 0 ? (
                  <tr>
                    <td colSpan="4" className="text-center py-4 text-gray-500">
                      No members found.
                    </td>
                  </tr>
                ) : (
                  filteredMembers.map((member, groupIdx) =>
                    member.locations.map((loc, idx) => {
                      return (
                        <tr
                          key={`${member.id}-${loc.tower || ''}-${loc.unit || ''}-${loc.role || ''}`}
                          className="hover:bg-gray-100 cursor-pointer"
                          onClick={() => handleSelect({ ...member, tower: loc.tower, unit: loc.unit, roles: [loc.role] })}
                        >
                          <td className="px-4 py-2 font-medium">
                            {idx === 0 ? member.full_name : ""}
                          </td>
                          <td className="px-4 py-2">
                            {loc.role === "Owner" ? "Owner" : loc.role || "—"}
                          </td>
                          <td className="px-4 py-2">
                            {loc.role === "Owner" ? (loc.tower || "—") : (loc.tower || "—")}
                          </td>
                          <td className="px-4 py-2">
                            {loc.role === "Owner" ? (loc.unit || "—") : (loc.unit || "—")}
                          </td>
                        </tr>
                      );
                    })
                  )
                )}
              </tbody>
            </table>
          </div>
        </>
      )}
    </div>
  );
};

export default MemberSearchAutocomplete;