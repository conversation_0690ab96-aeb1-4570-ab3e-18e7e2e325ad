
import React from 'react';
// Created By <PERSON><PERSON><PERSON>

function TextInputComponent({
  label,
  value,
  onChange,
  name,
  placeholder,
  width = '100%',
  error = '',
  disabled
}) {



  return (
    <div className="login-field " style={{ width }}>
      <div className="my-2 text-left">
        <label className="" htmlFor={label}>
          {label}
        </label>
      </div>
      <input
        type="text"
        name={name}
        value={value}
        disabled={disabled}
        // placeholder={placeholder}
        onChange={onChange}
        className={`
          login-field-input
          ${disabled ? 'bg-disabledInput cursor-not-allowed text-black100' : ''}
        ` }
        style={{ width }}
      />
      {/* <label className="login-field-label" htmlFor={label}>
        {label}
      </label> */}
      {error && <p className="text-red-500 text-xs">{error}</p>}
      
      
    </div>
  );
}

export default TextInputComponent;
