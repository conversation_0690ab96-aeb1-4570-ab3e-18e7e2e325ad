import React from 'react';

function RadioComponent({ options = [], selectedValue, onChange, name, label, width,disabled = false }) {
  return (
    <div className="radio-group pt-[35px] pr-3">
      <div className="text-base font-medium  pb-1">{label}</div>
      <div className="flex items-center" style={{ width }}>
        {options.length > 0 ? (
          options.map((option, index) => (
            <div className="flex items-center mr-[10px] mb-2" key={index}>
              <input
                type="radio"
                id={`${name}-${option.value}`}
                name={name}
                value={option.value}
                checked={selectedValue === option.value}
                onChange={onChange}

                className={` w-[16px] h-[16px] mr-2 accent-primary  ${disabled ? 'bg-disabledInput cursor-not-allowed text-black100' : 'cursor-pointer'} `}
               
                disabled={disabled}
              
 />
              <label htmlFor={`${name}-${option.value}`} className={`ml-2 ${disabled ? ' cursor-not-allowed ' : 'cursor-pointer'}`}>
                {option.label}
              </label>
            </div>
          ))
        ) : (
          <p className="text-gray-500">No options available</p>
        )}
      </div>
    </div>
  );
}

export default RadioComponent;
