 /* Created By <PERSON><PERSON><PERSON> */
.main-login {
    border: 1px solid #ddd;
}
.login-box {
    font-family: "Roboto", sans-serif;
    min-width: 450px;
    margin: 30px  0px;
    display: block;
    background: #fff;
    border-radius: 8px;
    text-align: center;
  }
  .login-title {
    color: #202124;
    margin-bottom: 0px;
    text-align: left !important;
  }
  
  .login-title-main {
    margin-bottom: 10px;
  }
  
  .login-title-sub {
    font-size: 15px;
    font-weight: 100;
  }
  
  .login-field {
    position: relative;
    margin-top: 15px;
    width: 100%;
  }
  
  .login-field-input {
    font-size: 18px;
    padding: 10px 15px;
    width: calc(100% - 0px);
    display: block;
    border-radius: 5px;
    /* border: 1px solid #ddd; */
    outline: none;
    transition: border-color 0.3s ease-in-out;
    border: 1px solid #79747E;

  }
  
  .login-field-label {
    color: #ccc;
    background: #fff;
    font-size: 16px;
    font-weight: normal;
    position: absolute;
    pointer-events: none;
    top: 9px;
    left: 8px;
    padding: 0 8px;
    transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  }
  
input::placeholder {
    font-size: 12px; 
    color: #aaa; 
  }
  .custom-input::placeholder {
    font-size: 12px;
    color: #aaa;
  }
  
  .login-field-input {
    font-size: 16px;
  }
  
  .login-field-input ~ .login-field-label {
    color: #79747E;
    transform: scale(0.75) translate(-13px, -30px);
  }
  
  .login-footer {
    margin-top: 50px;
  }
  
  .login-footer-text {
    color: #ccc;
    margin-bottom: 5px;
    font-size: 13px;
  }
  
  .login-footer-link {
    font-size: 10px;
    display: inline;
    transition: all 300ms ease-in-out;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    text-decoration: none;
  }
  
  .login-footer-link.dribbble {
    color: #ea4c89;
  }
  
  .login-footer-link.dribbble:hover {
    background: #ea4c89;
    color: #fff;
  }
  

.toggle-password {
    cursor: pointer;
    position: absolute;
    right: 3%; 
    top: 50%; 
    transform: translateY(-50%); 
    font-size: 18px;
    color: #555;
    pointer-events: none; 
  }
  
  .toggle-password:hover {
    color: #000;
  }
  

.login-options {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }
  
  .login-remember {
    display: flex;
    align-items: center;
  }
  
  .login-remember input {
    margin-right: 8px;
  }
  
  .login-forgot a {

    text-decoration: none;
  }
  
  .login-forgot a:hover {
    text-decoration: underline;
  }
  
  .login-footer {
    margin-top: 20px;
    text-align: center;
  }
  
  .or-text {
    font-size: 14px;
    margin-bottom: 10px;
  }
  

  /* checkbox style */






  
/* The parentdiv */
/* The container (checkbox with label) */
.parentdiv {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  font-size: 15px !important;
}

/* Hide the default checkbox */
.parentdiv input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0px;
  left: 0;
  height: 20px;
  width: 20px;
 border: 1px solid #ccc;
  transition: background-color 0.3s ease;
  border-radius: 5px;
}
/* On hover, change the background color */
.parentdiv:hover input ~ .checkmark {
  background-color: #ccc;
}

/* When the checkbox is checked, add a blue background */
.parentdiv input:checked ~ .checkmark {
  background-color: #3D9D9B;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.parentdiv input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.parentdiv .checkmark:after {
  left: 5px;
  top: 3px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
