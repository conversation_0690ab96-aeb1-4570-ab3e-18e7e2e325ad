import React from "react";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { clearTokens } from "../../utils/tokenUtils";
import { logout } from "../../redux/slices/authSlice/authSlice";

const NotAuthorized = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isAuthenticated = useSelector((state) => state.auth.isAuthenticated);

  // Log current auth status for debugging
  // console.log("isAuthenticated:", isAuthenticated); // This will help you debug

  const handleRedirect = () => {
    // Clear Redux and localStorage
    // dispatch(logout());  // Dispatch logout action to clear Redux state
    // clearTokens(); // Clear tokens from localStorage

    // Redirect to the appropriate route based on the previous authentication state
    if (isAuthenticated) {
      // User was authenticated, redirect to member list
      navigate("/member-list");
    } else {
      dispatch(logout());  // Dispatch logout action to clear Redux state
      clearTokens(); // Clear tokens from localStorage
      navigate("/login");
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="bg-white shadow-lg rounded-lg p-8 text-center animate-fadeIn">
        <h1 className="text-4xl font-bold text-red-600 mb-4">Access Denied</h1>
        <p className="text-gray-700 mb-6">
          You do not have the required permissions to view this page.
        </p>

        {/* Print authentication status */}
        <p className="text-lg text-gray-500">
          {isAuthenticated ? "You are authenticated!" : "You are not authenticated."}
        </p>

        <button
          onClick={handleRedirect}
          className="bg-blue-600 text-white px-6 py-3 rounded-md text-lg font-semibold hover:bg-blue-700 transition duration-300"
        >
          Go back to Home
        </button>
      </div>
    </div>
  );
};

export default NotAuthorized;
