import React from "react";
import user1 from "../../assets/user/user.png";

const UserThumbnail = ({ photoUrl }) => {
  return (
    <div className=" flex items-center justify-center rounded-full bg-gray-200 overflow-hidden">
      {photoUrl ? (
        <img
          src={`http://127.0.0.1:8000${photoUrl}`}
          alt="User"
          className="w-6 h-6 object-cover"
        />
      ) : (
        <img src={user1} alt="User" className="w-6 h-6 object-cover" />
      )}
    </div>
  );
};

export default UserThumbnail;
