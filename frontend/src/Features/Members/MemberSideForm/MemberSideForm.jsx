import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import TextInputComponent from "Components/FormComponent/TextInputComponent";
import TextareaComponent from "Components/FormComponent/TextareaComponent";
import SingleImageUpload from "../../../utils/SingleImageUpload";
import { RxCross2 } from "react-icons/rx";
import Heading from "Components/HeadingComponent/Heading";
import { Div } from "Components/Ui/Div";
import img1 from "../../../../public/login.jpg";
import DottedUserBox from "../../../Components/ImageBox/DottedUserBox";
import ErrorMessage from "../../../Components/MessageBox/ErrorMessage";

const MemberSideForm = ({
  memberFields,
  formData,
  setFormData,
  handleChange,
  onFileChange,
  allowedTypes,
  errorMessage = "Please upload a valid image",
  savedPhoto,
  disabled=false
}) => {
  const [fileObj, setFileObj] = useState(null); // for File or string (URL)
  const [previewUrl, setPreviewUrl] = useState(""); // always a string
  const [fileError, setFileError] = useState("");

  // Handle external savedPhoto (string URL)
  useEffect(() => {
    if (savedPhoto && typeof savedPhoto === "string" && savedPhoto.startsWith("http")) {
      setFileObj(null); // no File, just a URL
      setPreviewUrl(savedPhoto);
    }
  }, [savedPhoto]);

  // When fileObj changes to a File, make a preview URL
  useEffect(() => {
    if (fileObj && fileObj instanceof File) {
      const url = URL.createObjectURL(fileObj);
      setPreviewUrl(url);

      return () => {
        URL.revokeObjectURL(url);
      };
    } else if (!fileObj) {
      setPreviewUrl(""); // if cleared
    }
  }, [fileObj]);

  // Handle file input
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type) || file.size > maxSize) {
      setFileError("Only JPG, JPEG, PNG files under 5MB are allowed.");
      setFileObj(null);
      setFormData((prev) => ({ ...prev, photo: "" }));
      onFileChange("photo", "");
      return;
    }

    setFileError("");
    setFileObj(file);
    setFormData((prev) => ({ ...prev, photo: file }));
    onFileChange("photo", file);
  };

  // Remove file & preview
  const removeFile = () => {
    setFileObj(null);
    setFileError("");
    setFormData((prev) => ({ ...prev, photo: "" }));
    onFileChange("photo", "");
  };

  return (
<Div className=" bg-white rounded-l-xl  h-full">
   <div className="mb-2">   <Heading title="Upload Picture" size="lg" color="text-black" /></div>
      <Div>
        {previewUrl ? (
          <Div className="my-[20px] relative">
            <img
              src={previewUrl}
              alt="Profile"
              className="member-profile-image11 object-cover rounded-lg"
            />
            <button
              type="button"
              onClick={removeFile}
              className="absolute top-0 right-0 p-1 rounded-full bg-primary text-white"
            >
              <RxCross2 />
            </button>
          </Div>
        ) : (
          <DottedUserBox />
        )}
      </Div>
      {fileError && <ErrorMessage message={fileError} />}
      <Div className="w-full my-[20px]">
     

     <label
        htmlFor="photo"
        className={`  py-2 px-4 rounded w-full block text-center
          ${disabled ? 'bg-disabledInput cursor-not-allowed text-black100' : ' bg-primary cursor-pointer text-white'}
        `}
        onClick={e => disabled && e.preventDefault()} // prevent click if disabled
       >
        Upload Photo
      </label>


        <input
          id="photo"
          type="file"
          accept="image/jpeg, image/png, image/jpg"
          className="hidden"
          onChange={handleFileChange}
        />
      </Div>
      <Div>
        <TextareaComponent
          value={formData.about_us}
          onChange={handleChange}
          name={memberFields.about_us.name}
          label={memberFields.about_us.label}
          rows={memberFields.about_us.rows || 6}
          field={memberFields.about_us}
          disabled={disabled}
        />
        <TextInputComponent
          value={formData.facebook_profile}
          onChange={handleChange}
          name={memberFields.facebook_profile.name}
          label={memberFields.facebook_profile.label}
          field={memberFields.facebook_profile}
          placeholder={memberFields.facebook_profile.label}
          disabled={disabled}
        />
        <TextInputComponent
          value={formData.linkedin_profile}
          onChange={handleChange}
          name={memberFields.linkedin_profile.name}
          label={memberFields.linkedin_profile.label}
          field={memberFields.linkedin_profile}
          placeholder={memberFields.linkedin_profile.label}
          disabled={disabled}
        />
      </Div>
    </Div>
  );
};

MemberSideForm.propTypes = {
  memberFields: PropTypes.object.isRequired,
  formData: PropTypes.object.isRequired,
  setFormData: PropTypes.func.isRequired,
  handleChange: PropTypes.func.isRequired,
  onFileChange: PropTypes.func.isRequired,
  allowedTypes: PropTypes.array.isRequired,
  errorMessage: PropTypes.string,
  savedPhoto: PropTypes.string
};

export default MemberSideForm;