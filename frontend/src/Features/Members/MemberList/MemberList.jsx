import React, { useCallback, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useLocation, useNavigate } from "react-router-dom";
import { FaFileExcel, FaPrint, FaDownload } from "react-icons/fa";
import { useDispatch, useSelector } from "react-redux";
import MemberListTable from "Components/Table/Member/MemberListTable";
import { Div } from "Components/Ui/Div";
import Image from "Components/Ui/Image";
import SearchBar from "Components/Search/SearchBar";
import Heading from "Components/HeadingComponent/Heading";
import Button from "../../../Components/FormComponent/ButtonComponent/Button";
import FilterSelect2 from "../../../Components/FilterSelect/FilterSelect2";
import TableSkeleton from "../../../Components/Loaders/TableSkeleton";
import useMemberList from "../useMemberList";
import { fetchMemberTypes } from "../../../redux/slices/api/memberApi";
import { checkPermission } from "../../../utils/permissionUtils";
import { exportToExcel, printTable } from "utils/exportPrintExcel";
import { RiFilter3Fill } from "react-icons/ri";
// import { GoPlus } from "react-icons/go";
import { FaPlus } from "react-icons/fa6";

import logo from "./../../../assets/user/user.png";
import FilterSelect1 from "../../../Components/FilterSelect1/FilterSelect1";
import { fetchRoles } from "../../../redux/slices/groups/groupSlice";
import FilterButton from "../../../Components/FormComponent/ButtonComponent/FilterButton";

const MemberList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  // State declarations
  const [error, setError] = useState(null);
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);
  const [showFilters, setShowFilters] = useState(false); // New state for filter visibility
  const rolesFromRedux = useSelector((state) => state.group.roles);
    const [filterActive, setFilterActive] = useState(false);

  // const [appliedRole, setAppliedRole] = useState("");

  // Custom hooks and selectors
  const {
    members,
    loading: membersLoading,
    error: membersError
  } = useMemberList();

  const {
    memberTypes = [],
    loading: memberTypeLoading,
    error: memberTypeError
  } = useSelector((state) => state.member);

  // Hooks declarations
  useEffect(() => {
    dispatch(fetchMemberTypes());
    dispatch(fetchRoles());
  }, [dispatch]);

  useEffect(() => {
    setError(null);
  }, [location.pathname]);

  // Permission check
  useEffect(() => {
    const fetchPermission = async () => {
      const permissionGranted = await checkPermission("org", 3);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  const handleExport = useCallback(() => {
    const exportData = members.map((member) => ({
      Name: member.full_name,
      Contact: member.general_contact,
      Email: member.general_email,
      Type: member.member_type_name,
      Role: member.member_roles?.map((role) => role.role_name).join(", "),
      Status: Number(member.is_org_member) === 1 ? "Active" : "Inactive"
    }));

    exportToExcel(exportData, "EstateLink Member List");
  }, [members]);

  const handlePrint = useCallback(() => {
    const title = "Members List";
    const logoUrl = logo;
    const fileName = `EstateLink Member List ${
      new Date().toISOString().split("T")[0]
    }.pdf`;
    const columns = [
      { header: "Name", accessor: (item) => item.full_name },
      { header: "Contact", accessor: (item) => item.general_contact },
      { header: "Email", accessor: (item) => item.general_email },
      { header: "Type", accessor: (item) => item.member_type_name },
      {
        header: "Role",
        accessor: (item) =>
          item.member_roles?.map((role) => role.role_name).join(", ")
      },
      {
        header: "Status",
        accessor: (item) =>
          Number(item.is_org_member) === 1 ? "Active" : "Inactive"
      }
    ];

    printTable(members, columns, title, logoUrl, fileName);
  }, [members]);

  // Derived data
  const memberTypeOptions = memberTypes.map((item) => ({
    value: item.id,
    label: item.type_name
  }));

  // Toggle filter visibility
 

  // Conditional renders (AFTER all hooks)
  if (loadingPermission) {
    return <TableSkeleton />;
  }

  if (!hasPermission) {
    return (
      <div className="text-center p-8 text-red-500">
        You are not authorized to view this page.
      </div>
    );
  }

  // const memberRoleOptions = [
  //   ...rolesFromRedux
  //     .filter((role) => role.is_active)
  //     .map((role) => ({
  //       label: role.role_name,
  //       value: role.id.toString()
  //     })),
  //   { label: "Other", value: "other" }
  // ];

  const memberRoleOptions = rolesFromRedux.map((item) => ({
    value: item.id,
    label: item.role_name
  }));
  console.log(memberRoleOptions, "memberRoleOptions");
  const toggleFilters = () => {
    setShowFilters((prev) => !prev);
    setFilterActive((prev) => !prev); // this was missing!
  };
  return (
    <Div className="relative  bg-white ">
      <Div className="pb-4">
        <Div className="flex justify-between items-center py-4">
          <Heading title="Members List" size="xl" color="text-black" />
          <Div className="flex items-center space-x-4">
            <Button
              icon={FaDownload}
              variant="download"
              size="large"
              onClick={handleExport}
              disabled={!members.length}
              iconSize="medium"
            ></Button>
            <Button
              variant="download"
              size="large"
              icon={FaPrint}
              onClick={handlePrint}
              disabled={!members.length}
              iconSize="medium"
            ></Button>
            {/* <Button
              variant="download"
              size="xl"
              iconSize="xl"
              icon={RiFilter3Fill}
              onClick={toggleFilters}
            >
              Filter
            </Button> */}

            <FilterButton active={filterActive} onClick={toggleFilters}>
              Filter
            </FilterButton>
            <Button
              icon={FaPlus}
              onClick={() =>
                navigate("/create-member", {
                  state: { showMessage: false }
                })
              }
              className="bg-primary  text-center hover:bg-primary-dark text-white"
            >
              Add Member
            </Button>
          </Div>
        </Div>

        {/* Filter section that appears when showFilters is true */}
        {showFilters && (
          <Div className="flex justify-end items-center space-x-4 mt-4">
            {/* <FilterSelect1
              placeholder="Role"
              options={memberRoleOptions}
              paramKey="role"
              useUrlParams={true} 
              // onApply={setAppliedRole}
            /> */}
            <FilterSelect2
              placeholder="Role"
              options={memberRoleOptions}
              paramKey="role"
              onApply={(filters) => {
                if (!filters) return;
                console.log("Applied Filters:", filters);
              }}
            />
            <FilterSelect2
              placeholder="Member Type"
              options={memberTypeOptions}
              paramKey="member_type"
              onApply={(filters) => {
                if (!filters) return;
                console.log("Applied Filters:", filters);
              }}
            />
            <SearchBar />
          </Div>
        )}
      </Div>

      {/* Loading and Error Handling */}
      {membersLoading || memberTypeLoading ? (
        <div className="flex items-center justify-center my-12">
          <TableSkeleton />
        </div>
      ) : members.length === 0 ? (
        <div className="text-center my-12 text-gray-500">No results found</div>
      ) : (
        <MemberListTable member={members} error={membersError} />
      )}
    </Div>
  );
};

MemberList.propTypes = {
  members: PropTypes.array,
  memberTypes: PropTypes.array
};

MemberList.defaultProps = {
  members: [],
  memberTypes: []
};

export default MemberList;
