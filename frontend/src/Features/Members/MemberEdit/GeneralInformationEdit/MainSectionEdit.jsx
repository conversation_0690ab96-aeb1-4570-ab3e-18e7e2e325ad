import DatePicker from "react-datepicker";
import TextInputComponent from "../../../../Components/FormComponent/TextInputComponent";
import RadioComponent from "../../../../Components/FormComponent/RadioComponent";
import SelectComponent from "../../../../Components/FormComponent/SelectComponent";
import ErrorMessage from "../../../../Components/MessageBox/ErrorMessage";
import { Div } from "../../../../Components/Ui/Div";

const MainSectionEdit = ({ formData, handleChange, errors, touched }) => {
  const formatDate = (date) => {
    const day = date.getDate().toString().padStart(2, "0");
    const month = date.toLocaleString("en-GB", { month: "short" });
    const year = date.getFullYear();
    let formattedDate = `${day}-${month}-${year}`;
    formattedDate = formattedDate.replace("Sept", "Sep");
    return formattedDate;
  };
  const currentYear = new Date().getFullYear(); // Get the current year

  return (
    <>
      {/* Full Name */}
      <TextInputComponent
        value={formData.full_name}
        onChange={handleChange}
        name="full_name"
        label={
          <span>
            Full Name <span className="text-red-500">*</span>
          </span>
        }
        placeholder="Full Name"
      />
      {errors.full_name && touched.full_name && (
        <ErrorMessage message={errors.full_name} />
      )}

      {/* <ErrorM message={errors.general_email} /> */}

      {/* Email */}
      <TextInputComponent
        value={formData.general_email}
        onChange={handleChange}
        name="general_email"
        label={
          <span>
            Email <span className="text-red-500">*</span>
          </span>
        }
        placeholder="Email"
      />
      {errors.general_email && touched.general_email && (
        <ErrorMessage message={errors.general_email} />
      )}

      {/* Contact Number */}
      <TextInputComponent
        value={formData.general_contact}
        onChange={handleChange}
        name="general_contact"
        label={
          <span>
            Contact Number <span className="text-red-500">*</span>
          </span>
        }
        placeholder="Contact Number"
      />
      {/* {errors.general_contact && touched.general_contact && (
        <div>{errors.general_contact}</div>
      )} */}
      {errors.general_contact && touched.general_contact && (
        <ErrorMessage message={errors.general_contact} />
      )}

      {/* NID Number (No validation) */}
      <TextInputComponent
        value={formData.nid_number}
        onChange={handleChange}
        name="nid_number"
        label="NID Number"
        placeholder="NID Number"
      />
      {errors.nid_number && touched.nid_number && (
        <ErrorMessage message={errors.nid_number} />
      )}

      {/* Permanent Address (No validation) */}
      <TextInputComponent
        value={formData.permanent_address}
        onChange={handleChange}
        name="permanent_address"
        label="Permanent Address"
        placeholder="Permanent Address"
      />

      {/* Present Address (No validation) */}
      <TextInputComponent
        value={formData.present_address}
        onChange={handleChange}
        name="present_address"
        label="Present Address"
        placeholder="Present Address"
      />

      {/* Date of Birth (No validation) */}
      {/* <div className="login-field">
        <div className="login-field">
          <DatePicker
            className="login-field-input"
            name="date_of_birth"
            selected={
              formData.date_of_birth ? new Date(formData.date_of_birth) : null
            }
            onChange={(date) => {
              handleChange({
                target: { name: "date_of_birth", value: formatDate(date) }
              });
            }}
            dateFormat="dd-MMM-yyyy"
            placeholderText="Date of Birth"
          />
        </div>
      </div> */}
            <Div class=" flex justify-between item-center gap-4 ">
      
      <div className="login-field">
      <div className="my-2 text-left">
            <label className="">Date Of Birth</label>
          </div>
        <DatePicker
          className="login-field-input"
          name="date_of_birth"
          selected={
            formData.date_of_birth ? new Date(formData.date_of_birth) : null
          }
          onChange={(date) => {
            handleChange({
              target: { name: "date_of_birth", value: formatDate(date) }
            });
          }}
          dateFormat="dd-MMM-yyyy"
          // placeholderText="Date Of Birth"
          showYearDropdown
          showMonthDropdown
          scrollableYearDropdown
          yearDropdownItemNumber={90}
          minDate={new Date("1950-01-01")}
          maxDate={new Date(`${currentYear}-12-31`)} // Set maxDate to the current year
        />
      </div>

      {/* Occupation & Gender (No validation) */}
      {/* <div className="flex justify-between gap-5"> */}
        <TextInputComponent
          value={formData.occupation}
          onChange={handleChange}
          name="occupation"
          label="Occupation"
          placeholder="Occupation"
        />
        <RadioComponent
          options={[
            { label: "Male", value: "Male" },
            { label: "Female", value: "Female" },
            { label: "Other", value: "Other" }
          ]}
          selectedValue={formData.gender}
          onChange={handleChange}
          name="gender"
          label="Gender"
        />
      </Div>
      <div className="flex justify-between gap-3">
        <SelectComponent
          options={[
            { label: "Select Marital Status", value: "" },

            { label: "Single", value: "Single" },
            { label: "Married", value: "Married" },
            { label: "Divorced", value: "Divorced" },
            { label: "Widowed", value: "Widowed" }
          ]}
          value={formData.marital_status}
          name="marital_status"
          label="Select Marital Status"
          onChange={handleChange}
        />

        <SelectComponent
          options={[
            { label: "Select Religion", value: "" },

            { label: "Islam", value: "Islam" },
            { label: "Christianity", value: "Christianity" },
            { label: "Hinduism", value: "Hinduism" },
            { label: "Buddhism", value: "Buddhism" },
            { label: "Judaism", value: "Judaism" },
            { label: "Other", value: "Other" }
          ]}
          value={formData.religion}
          name="religion"
          label=" Select Religion"
          onChange={handleChange}
        />
      </div>
    </>
  );
};

export default MainSectionEdit;
