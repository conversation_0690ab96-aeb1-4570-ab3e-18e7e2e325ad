import React from "react";
import { RxCross2 } from "react-icons/rx";
import TextareaComponent from "../../../../Components/FormComponent/TextareaComponent";
import TextInputComponent from "../../../../Components/FormComponent/TextInputComponent";
import Heading from "../../../../Components/HeadingComponent/Heading";
import { Div } from "../../../../Components/Ui/Div";
import SingleImageUpload from "../../../../utils/SingleImageUpload";
import nid from "../../../../../public/nid.png";
import user from "../../../../assets/user/user.png";
import ErrorMessage from "../../../../Components/MessageBox/ErrorMessage";
const SideSectionEdit = ({
  formData,
  handleChange,
  handleFileChange,
  removeFile,
  fileErrors,
}) => {
  return (
    <Div className="p-7 pe-12 bg-white rounded-l-xl border border-[#F9F9FB]">
      <Heading title="Upload Picture" size="lg" color="text-black" />
      {/* Profile Picture Upload */}
      <Div className="my-[20px] flex justify-center items-center">
        <Div className="relative py-1">
          <SingleImageUpload
            file={formData.photo}
            altImg={user}
            customClass="member-profile-image11 object-cover"
          />
          {formData.photo && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                removeFile("photo");
              }}
              className="absolute top-0 right-0 p-1 rounded-full bg-primary text-white"
            >
              <RxCross2 />
            </button>
          )}
        </Div>
      </Div>
      {/* File Upload Input */}
      {fileErrors.photo && <ErrorMessage message={fileErrors.photo} />}

      <Div className="my-[10px]">
        <label
          htmlFor="photo"
          className="cursor-pointer bg-primary text-white py-2 px-4 rounded w-full block text-center"
        >
          {formData.photo ? "Change Photo" : "Upload Photo"}
        </label>
        <input
          id="photo"
          type="file"
          accept="image/*"
          className="hidden"
          onChange={(e) => handleFileChange(e, "photo")}
        />
      </Div>
      {/* Additional Fields */}
      <Div>
        <TextareaComponent
          value={formData.about_us}
          onChange={handleChange}
          name="about_us"
          label="About Us"
          rows={6}
        />
        <TextInputComponent
          value={formData.facebook_profile}
          onChange={handleChange}
          name="facebook_profile"
          label="Facebook Profile"
          placeholder="Facebook Profile"
        />
        <TextInputComponent
          value={formData.linkedin_profile}
          onChange={handleChange}
          name="linkedin_profile"
          label="LinkedIn Profile"
          placeholder="LinkedIn Profile"
        />
      </Div>
    </Div>
  );
};
export default SideSectionEdit;
