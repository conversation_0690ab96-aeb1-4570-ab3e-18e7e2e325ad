import Button from "../../../Components/FormComponent/ButtonComponent/Button";
import DynamicEditLink from "./DynamicLinkEditButton";

const OrganizationMemberInformationView = ({ member }) => {
  return (
    <>
      <div className="mx-auto border rounded-lg p-3 ">
        <div className="grid grid-cols-3 gap-4">
          <div className="col-span-3">
            <div className="flex justify-between my-3">
              <h2 className="text-lg font-bold text-primary">
                Organization Member Information
              </h2>
              <DynamicEditLink
                basePath="MemberTypeAndRoleEdit"
                resourceId={member?.id}
              />
            </div>

            <div className="py-2">
              <p className="text-grey100 text-sm pb-1 text-left">Type</p>
              <p>{member?.member_type_name || "--"}</p>
            </div>

            <div className=" ">
              <p className="text-grey100 text-sm pb-1 text-left">Role</p>


              <div className="grid grid-cols-6 gap-2 col-span-5 text-left">
              {member?.member_roles?.length > 0 ? (
                      [...new Map(
                        member.member_roles.map(role => [role.id, role])
                      ).values()].map(role => (
                        <Button key={role.id} size="small" variant="black">
                          {role.role_name}
                        </Button>
                      ))
                    ) : (
                      "--"
                    )}




              </div>
            </div>

            <div className="  ">
              <p className="text-grey100 text-sm pt-1 text-left">Groups</p>
              <div className="grid grid-cols-5 gap-2 col-span-5 text-left">
                {member?.member_groups?.length > 0
                  ? member.member_groups.map((group) => (
                    <p
                      key={group.id}
                      className=" text-base  py-1 "
                    >
                      {group.group_name}
                    </p>
                  ))
                  : "--"}
              </div>
            </div>

          </div>
        </div>
      </div>
    </>
  );
};

export default OrganizationMemberInformationView;
