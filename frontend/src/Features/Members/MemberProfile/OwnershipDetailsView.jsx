import React from "react";
import EditButton from "../../../Components/Buttons/EditButton";
import FilePreviewWithDownload from "../../TowersAndUnits/UnitDetails/components/FilePreviewWithDownload";
import DynamicLinkEditButton from "./DynamicLinkEditButton";
// import FilePreviewWithDownload from "../../../Components/FilePreviewWithDownload";

const OwnershipDetailsView = ({ unit, editPermission, onEditClick }) => {

  return (
    <div className="border border-gray-200 rounded-xl p-6 my-4 m-2">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-800">
        <span >    Unit </span>
          {unit.unit_name}
        </h2>
        {/* <EditButton onClick={onEditClick} /> */}
        <DynamicLinkEditButton
          basePath="/unit/:unitid/change-owner"
          params={{
            unitid: unit?.unit,
          }}
        />
        {/* path: "unit/:unitId/change-owner", */}
        </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <p className="text-sm text-gray-500">Ownership Date</p>
          <p className="text-base font-medium text-gray-800">
            {unit.date_of_ownership
              ? new Date(unit.date_of_ownership).toLocaleDateString()
              : "N/A"}
          </p>
        </div>

        <div>
          <p className="text-sm text-gray-500">Ownership Percentage</p>
          <p className="text-base font-medium text-gray-800">
            {unit.ownership_percentage
              ? `${parseFloat(unit.ownership_percentage).toFixed(2)}%`
              : "N/A"}
          </p>
        </div>

        <div>
          <p className="text-sm text-gray-500">Tower Name</p>
          <p className="text-base font-medium text-gray-800">
            {unit.tower?.tower_name || "N/A"}
          </p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Unit Name</p>
          <p className="text-base font-medium text-gray-800">
            {unit.unit_name || "N/A"}
          </p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Tower Number</p>
          <p className="text-base font-medium text-gray-800">
            {unit.tower?.tower_number || "N/A"}
          </p>
        </div>
      </div>

      {unit.owner_docs?.length > 0 && (
        <div className="mt-6">
          <p className="text-sm text-gray-500 mb-2">Documents</p>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {unit.owner_docs.map((doc, index) => (
              <FilePreviewWithDownload
                key={index}
                filePath={doc.url} // ← fix this line
                fileName={doc.name || `Document_${index + 1}`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default OwnershipDetailsView;
