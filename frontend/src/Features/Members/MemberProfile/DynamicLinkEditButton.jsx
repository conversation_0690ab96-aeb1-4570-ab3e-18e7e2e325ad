import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import editIcon from "../../../assets/edit-02.png";
import { useEffect } from 'react';
import { MdEdit } from "react-icons/md";


const DynamicLinkEditButton = ({ 
  basePath,
  resourceId,
  params = {},
  label = "Edit",
  icon = editIcon,
  onClick,
  className = "",
  ariaLabel = "Edit button"
}) => {
  const generateUrl = () => {
    if (Object.keys(params).length > 0) {
      let generatedPath = basePath;
      Object.entries(params).forEach(([key, value]) => {
        generatedPath = generatedPath.replace(`:${key}`, value);
      });
      return generatedPath;
    }
    
    return `/${basePath}/${resourceId}`.replace(/\/+/g, '/');
  };

  useEffect(() => {
    if (!resourceId && Object.keys(params).length === 0) {
      console.error("Error: Either resourceId or params must be provided");
    }
  }, [resourceId, params]);

  return (
    <Link 
      to={generateUrl()}
      className={`inline-block ${className}`}
      aria-label={ariaLabel}
      style={{ cursor: "pointer" }}
    >
      <button
  onClick={onClick}
  className="flex items-center justify-center gap-2 py-1 px-3 rounded-lg border-2 border-primary hover:bg-primary/10 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary/50"
  type="button"
>
  <MdEdit className="text-primary text-xl" /> {/* 24px size */}
  <span className="text-primary font-semibold text-base">
    {label}
  </span>
</button>
    </Link>
  );
};

DynamicLinkEditButton.propTypes = {
  basePath: PropTypes.string.isRequired,
  resourceId: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number
  ]),
  params: PropTypes.object,
  label: PropTypes.string,
  icon: PropTypes.string,
  onClick: PropTypes.func,
  className: PropTypes.string,
  ariaLabel: PropTypes.string
};

export default DynamicLinkEditButton;