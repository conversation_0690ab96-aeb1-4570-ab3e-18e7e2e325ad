import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Info from "../../../Components/Ui/Info";
import DynamicEditLink from "./DynamicLinkEditButton";
// import ConfirmationMessageBox from "../../../Components/Ui/ConfirmationMessageBox"; // Make sure to import this
import {
  changeOrgMemberStatus,
  fetchMemberById
} from "../../../redux/slices/api/memberApi";
import { setActiveTabs } from "../../../redux/slices/memberSlice";
import ConfirmationMessageBox from "Components/MessageBox/ConfirmationMessageBox";
import MessageBox from "Components/MessageBox/MessageBox";

const OrgLoginCredentialEditView = () => {
  const dispatch = useDispatch();
  const statusLoading = useSelector((state) => state.member.statusLoading);
  const memberData = useSelector(
    (state) => state.member.selectedMember?.member
  );
  
  // State for confirmation modals
  const [showStatusConfirm, setShowStatusConfirm] = useState(false);
  const [pendingStatus, setPendingStatus] = useState(null);
    const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  

  const handleStatusChange = (status) => {
    if (!memberData) return;
    dispatch(
      changeOrgMemberStatus({
        id: memberData?.id,
        status,
        member_type: "org"
      })
    ).then(() => {
      dispatch(fetchMemberById(memberData?.id));
    });
  };

  // Show confirmation dialog and cache pending status
  const showConfirmation = (status) => {
    setPendingStatus(status);
    setShowStatusConfirm(true);
  };

  // Handle confirmed status change
 const handleConfirmStatusChange = () => {
  if (pendingStatus !== null) {
    handleStatusChange(pendingStatus);
    setShowSuccessMessage(true); // show success message
  }
  setShowStatusConfirm(false);
};

  return (
    <div className="mx-auto p-3 mb-3 border rounded-lg">
      {/* Status Change Confirmation Modal */}
      {showStatusConfirm && (
        <ConfirmationMessageBox
          message={`Do you want to ${
            pendingStatus === 1 ? "activate" : "deactivate"
          } this login credential?`}
          onConfirm={handleConfirmStatusChange}
          onCancel={() => setShowStatusConfirm(false)}
        />
      )}
      {showSuccessMessage && (
              <MessageBox
                message="Status updated successfully."
                clearMessage={() => setShowSuccessMessage(false)}
                onOk={() => setShowSuccessMessage(false)}
              />
            )}

      <div className="flex justify-between mb-4">
        <h2 className="text-lg font-bold text-primary">Login Credential</h2>
        <div className="flex">
          {memberData?.is_org_member == 1 ? (
            <button
              className="mx-2 font-semibold py-1 px-2 rounded-lg bg-primary text-white"
              disabled={statusLoading}
              onClick={() => showConfirmation(0)}
            >
              {statusLoading ? "Updating..." : "Active"}
            </button>
          ) : (
            <button
              className="mx-2 font-semibold py-1 px-2 rounded-lg bg-error text-white"
              disabled={statusLoading}
              onClick={() => showConfirmation(1)}
            >
              {statusLoading ? "Updating..." : "Inactive"}
            </button>
          )}

          {memberData?.is_org_member == 1 && (
            <DynamicEditLink
              basePath="/login-credential-edit/:id"
              onClick={() => dispatch(setActiveTabs(2))}
              params={{
                id: memberData?.id
              }}
            />
          )}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        {memberData?.is_org_member == 1 ? (
          <div className="col-span-3">
            <div className="py-2">
              <Info label="User Name">{memberData?.username || "--"}</Info>
            </div>
            <div className="py-2">
              <Info label="E-mail/Phone number">
                {memberData?.login_email || memberData?.login_contact || "--"}
              </Info>
            </div>
          </div>
        ) : (
          <div className="col-span-3">
            <div className="py-2">
              <Info label="User Name">{"--"}</Info>
            </div>
            <div className="py-2">
              <Info label="E-mail/Phone number">{"--"}</Info>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrgLoginCredentialEditView;