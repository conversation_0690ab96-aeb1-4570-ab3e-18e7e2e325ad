import React, { useEffect, useState } from "react";
import { FiX } from "react-icons/fi";
import { useDispatch, useSelector } from "react-redux";
import { fetchAddExistingMembers } from "../../../redux/slices/api/memberApi";
import { Div } from "Components/Ui/Div";
import SearchBar from "Components/Search/SearchBar";
import Heading from "Components/HeadingComponent/Heading";
import FilterSelect2 from "../../../Components/FilterSelect/FilterSelect2";
import { useSearchParams } from "react-router-dom";
import { fetchMemberTypeOptions } from "./memberTypeList";
import { RxCross1 } from "react-icons/rx";

const AddExistingCommMemberTable = ({ isOpen, onClose, onSelect }) => {
  const BASE_URL = import.meta.env.VITE_BASE_API;
  const dispatch = useDispatch();
  const [selectUnit, setSelectUnit] = useState([]);
  const [filters, setFilters] = useState({ member_type: [], search: "" });
  const [searchParams, setSearchParams] = useSearchParams();

  const allMemberData = useSelector((state) => state.member?.AddExistingCommMember) || {};

  const allMembers = [
    ...(allMemberData.owners || []),
    ...(allMemberData.comm_members || []),
    ...(allMemberData.resident_members || []),
    ...(allMemberData.unit_staff || []),
  ];

  const getMemberValue = (member, key) => {
    if (key === "member_type_name") {
      if (member?.resident_member?.[key]) return "Resident";
      if (member?.member?.[key] && !member?.unit_staff) return "Owner";
      if (member?.member?.[key] && member?.unit_staff) return "Unit Staff";
      return member?.[key] || "-";
    }

    return member?.resident_member?.[key] || member?.member?.[key] || member?.[key] || "-";
  };

  const groupedMembers = allMembers.reduce((acc, member) => {
    const memberId = member?.id || member?.member?.id || member?.resident_member?.id;
    if (!acc[memberId]) {
      acc[memberId] = {
        ...member,
        locations: []
      };
    }
    acc[memberId].locations.push({
      type: getMemberValue(member, "member_type_name"),
      tower: getMemberValue(member, "tower_name"),
      floor: getMemberValue(member, "floor_no"),
      unit: getMemberValue(member, "unit_name")
    });
    return acc;
  }, {});

  useEffect(() => {
    const fetchOptions = async () => {
      const options = await fetchMemberTypeOptions();
      setSelectUnit(options);
    };
    fetchOptions();
  }, []);

  useEffect(() => {
    const roleFilter = searchParams.get("member_type");
    const searchQuery = searchParams.get("search");

    const roleFilterArray = roleFilter
      ? roleFilter.split(",").map((item) => (/^\d+$/.test(item) ? Number(item) : item))
      : [];

    const updatedFilters = {
      member_type: roleFilterArray,
      search: searchQuery || "",
    };

    setFilters(updatedFilters);
    dispatch(fetchAddExistingMembers({ filters: updatedFilters }));
  }, [searchParams, dispatch]);

  if (!isOpen) return null;

  const handleClose = () => {
    onClose();
    setSearchParams({});
  };

  const handleFilterApply = (filters) => {
    if (!filters) {
      console.error("Filters object is undefined");
      return;
    }
    console.log("Applied Filters:", filters);
  };

  const renderMemberRow = (member, location, isMainRow = false) => (
    <tr key={`${member?.id}-${location.type}-${isMainRow}`} className="bg-white hover:bg-gray-50">
      {isMainRow ? (
        <>
          <td className="px-4 py-3 flex items-center gap-2 border-b border-gray-200">
            <img
              src={
                member?.photo ||
                member?.resident_member?.photo ||
                member?.member?.photo
                  ? `${BASE_URL}${member?.photo || member?.resident_member?.photo || member?.member?.photo}`
                  : "/user.jpg"
              }
              onError={(e) => (e.target.src = "/user.jpg")}
              alt={getMemberValue(member, "full_name")}
              className="w-8 h-8 rounded-full object-cover"
            />
            {getMemberValue(member, "full_name")}
          </td>
          <td className="px-4 py-3 border-b border-gray-200">
            {getMemberValue(member, "general_contact")}
          </td>
          <td className="px-4 py-3 border-b border-gray-200">
            {getMemberValue(member, "general_email")}
          </td>
        </>
      ) : (
        <>
          <td className="px-4 py-3"></td>
          <td className="px-4 py-3"></td>
          <td className="px-4 py-3"></td>
        </>
      )}
      <td className="px-4 py-3 capitalize border-b border-gray-200">{location.type}</td>
      <td className="px-4 py-3 border-b border-gray-200">{location.tower}</td>
      <td className="px-4 py-3 border-b border-gray-200">{location.floor}</td>
      <td className="px-4 py-3 capitalize border-b border-gray-200">{location.unit}</td>
      <td className={`px-4 py-3 text-center ${isMainRow ? 'border-b border-gray-200' : ''}`}>
        {isMainRow && (
          <button
            onClick={() => onSelect(member)}
            className="bg-teal-600 hover:bg-teal-700 text-white px-3 py-1 rounded text-sm"
          >
            Add
          </button>
        )}
      </td>
    </tr>
  );

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="relative bg-white rounded-xl p-6 w-full md:max-w-[1100px] mx-4">
        <button

         onClick={() => {
          onClose();
          setSearchParams({});
        }}
        
                  className="absolute -top-[8px] -right-[8px] p-2 rounded-full bg-primary text-white shadow-md hover:bg-primary/90 transition z-20"
        >
          <RxCross1  />
        </button>


       

        <Div className="flex justify-between items-center py-4">
          <Heading title="Members List" size="lg" color="text-black" />
          <Div className="flex items-center space-x-4 px-5">
            <FilterSelect2
              placeholder="Select Type"
              options={selectUnit}
              paramKey="member_type"
              onApply={handleFilterApply}
            />
            <SearchBar />
          </Div>
        </Div>

        <div className="overflow-x-auto overflow-y-auto max-h-[500px]">
          {Object.values(groupedMembers).length > 0 ? (
            <table className="min-w-full text-sm text-left border border-gray-200">
              <thead className="bg-teal-50">
                <tr>
                  {["Name", "Contact", "Email", "Type", "Tower", "Floor", "Unit", "Action"].map(
                    (title, i) => (
                      <th key={i} className="px-4 py-3 font-semibold text-gray-800 border-b">
                        {title}
                      </th>
                    )
                  )}
                </tr>
              </thead>
              <tbody>
                {Object.values(groupedMembers).map((member) => (
                  <React.Fragment key={member?.id || member?.member?.id || member?.resident_member?.id}>
                    {member.locations.map((location, index) =>
                      renderMemberRow(member, location, index === 0)
                    )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="text-center py-8 text-gray-500">No members available</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AddExistingCommMemberTable;
