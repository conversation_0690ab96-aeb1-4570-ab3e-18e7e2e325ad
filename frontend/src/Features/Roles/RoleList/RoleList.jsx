import React, { useEffect, useState } from "react";
import RoleListTable from "../../../Components/Table/Role/RoleListTable";
import { checkPermission } from "../../../utils/permissionUtils";
import { useNavigate } from 'react-router-dom';
import TableSkeleton from "../../../Components/Loaders/TableSkeleton";
const RoleList = () => {
  const navigate = useNavigate();
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);

  useEffect(() => {
    const fetchPermission = async () => {
      const permissionGranted = await checkPermission("org", 6); 
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  if (loadingPermission) {
    return  <TableSkeleton />; // Show loading state while checking permission
  }

  if (!hasPermission) {
    navigate("/not-authorized");  
  }

  return (
    <div className="">
      <div className="container">
        <div className="p-[14px]">
          <RoleListTable addRole="addRole" roleProfile="roleProfile" />
        </div>
      </div>
    </div>
  );
};

export default RoleList;