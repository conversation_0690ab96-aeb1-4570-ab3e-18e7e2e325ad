import React, { useState, useEffect } from "react";
import { Link, useParams, useNavigate } from "react-router-dom";
import { BiArrowBack } from "react-icons/bi";
import { useDispatch, useSelector } from "react-redux";
import editIcon from "../../../assets/edit/edit-02.png";
import user1 from "../../../assets/user/user.png";
import {
  fetchRoleDetails,
  toggleRoleStatus
} from "../../../redux/slices/roles/rolesSlice";
import { checkPermission } from "../../../utils/permissionUtils";
import CheckboxComponent from "../../../Components/FormComponent/CheckboxComponent";
const baseURL = import.meta.env.VITE_BASE_API;

const RoleProfile = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { roleDetails, loading, error } = useSelector((state) => state.role);
  const [activeTab, setActiveTab] = useState(1);
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);

  useEffect(() => {
    // Check if the user has permission to view role details
    const checkUserPermissions = async () => {
      const permissionGranted = await checkPermission("org", 6);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };

    checkUserPermissions();
  }, []);

  // Fetch role details if the user has permission
  useEffect(() => {
    if (hasPermission && id) {
      dispatch(fetchRoleDetails(id));
    }
  }, [dispatch, id, hasPermission]);

  if (!hasPermission) {
    return <div>You are not authorized to view this page.</div>;
  }

  if (loading) return <p></p>;
  if (error) return <p>Error: {error}</p>;
  if (!roleDetails) return null;

  const {
    role_name,
    role_description,
    is_active,
    selected_permissions,
    all_permissions,
    assigned_members
  } = roleDetails;

  const handleToggleStatus = async () => {
    // Check central permission before toggling active/inactive role
    const centralPermissionGranted = await checkPermission("org", 5);
    if (!centralPermissionGranted) {
      navigate("/not-authorized");
      return;
    }
    dispatch(toggleRoleStatus(id));
  };
  console.log("roleDetails", roleDetails);
  return (
    <div className=" h-full">
      <div className="container">
        <div className="md:flex justify-between  py-[14px]">
          {/* Back Button */}
          <Link
            to="/role-list"
            className="flex items-center font-medium text-[24px]"
          >
            <BiArrowBack className="mr-2 text-gray-600 cursor-pointer" />
            <span className="font-semibold">View Role</span>
          </Link>

          {/* Action Buttons */}
          <div className="flex gap-4">
            {/* Toggle Status Button (replacing Delete) */}
            {/* <button
              onClick={handleToggleStatus}
              className={`flex items-center py-[8px]  px-5 text-sm rounded-[8px] border cursor-pointer ${
                is_active
                  ? "border-primary bg-primary text-white" // Active: Green border, white bg
                  : "border-error bg-error text-white" // Inactive: Red bg, white text
              }`}
            >
              <span className="px-1 text-base">
                {is_active ? "Active" : "Inactive"}
              </span>
            </button> */}

            {/* Edit Button */}
            <Link to={`/addRole/${id}`}>
              <p className="flex items-center bg-primary py-[8px] px-5 text-sm rounded-[8px] border border-[#3D9D9B] cursor-pointer">
                <span className="text-[19px]  px-[3px] text-[#1B1F26B8]">
                  <img src={editIcon} alt="edit" />
                </span>
                <span className="px-1 text-base text-white">Edit</span>
              </p>
            </Link>
          </div>
        </div>

        <div className="rounded-[27px] bg-white">
          <div className="p-4">
            {/* Tab Navigation */}
            <div className="flex mb-4 bg-subprimary">
              <button
                className={`flex-1 w-full py-2 font-[600] ${
                  activeTab === 1
                    ? "border border-primary"
                    : "border border-white"
                }`}
                onClick={() => setActiveTab(1)}
              >
                Role Details
              </button>
              <button
                className={`flex-1 w-full py-2 font-[600] ${
                  activeTab === 2
                    ? "border border-primary"
                    : "border border-white"
                }`}
                onClick={() => setActiveTab(2)}
              >
                Role Assigned Member
              </button>
            </div>

            {/* Tab Content */}
            <div className="mx-auto">
              {activeTab === 1 && (
                <div>
                  <div className="pb-[10px]">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="col-span-3">
                        <div className="py-2">
                          <p className="text-[#1B1F26B8] text-sm">Role Name</p>
                          <p className="text-base font-medium">{role_name}</p>
                        </div>
                        <div className="py-2">
                          <p className="text-[#1B1F26B8] text-sm">
                            Role Description
                          </p>
                          <p className="text-base font-medium">
                            {role_description}
                          </p>
                        </div>
                        {/* <div className="py-2">
                          <p className="text-[#1B1F26B8] text-sm">Status</p>
                          <p className="text-base font-medium">
                            {is_active ? "Active" : "Inactive"}
                          </p>
                        </div> */}
                      </div>
                    </div>
                  </div>
                  {/* Permission List */}
                  <div className="grid grid-cols-4 gap-4">
                    <div>
                      <div className="mb-[8px]">
                        <h2 className="text-base font-semibold p-[10px] text-center bg-subprimary rounded-[8px]">
                          Permission Management
                        </h2>
                      </div>
                      <div className="flex flex-col p-4 rounded-[10px] bg-white border shadow">
                        {/* {all_permissions &&
                          all_permissions.map((perm) => (
                            <div key={perm.id} className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 "
                                disabled
                                checked={selected_permissions.includes(perm.id)}
                              />
                              <label className="ml-2 mb-[3px]">{perm.permission_name}</label>
                            </div>
                          ))} */}
                        {all_permissions &&
                          all_permissions.map((perm) => (
                            <CheckboxComponent
                              key={perm.id}
                              name={`permission-${perm.id}`}
                              label={perm.permission_name}
                              checked={selected_permissions.includes(perm.id)}
                              // disabled={true} // disable checkbox since in your code disabled
                            />
                          ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 2 && (
                <div>
                  <div className="bg-white">
                    <div>
                      <table className="w-full text-sm text-left">
                        <thead className="bg-subprimary shadow-lg border-b border-subprimary">
                          <tr>
                            <th className="px-3 font-[700] py-2 text-base text-left">
                              Name
                            </th>
                            <th className="px-3 font-[700] py-2 text-base text-left">
                              Contact
                            </th>
                            <th className="px-3 font-[700] py-2 text-base text-left">
                              Type
                            </th>
                            <th className="px-3 font-[700] py-2 text-base text-left">
                              Email
                            </th>
                            <th className="px-3 font-[700] py-2 text-base text-left">
                              Role
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {assigned_members && assigned_members.length > 0 ? (
                            assigned_members.map((member) => (
                              <tr
                                key={member.id}
                                className="bg-white border-b hover:bg-gray-50"
                              >
                                <td className="px-3 py-[10px] text-[14px] text-left">
                                  <div className="flex items-center">
                                    <img
                                      src={
                                        member.photo
                                          ? `${baseURL}${member.photo}`
                                          : user1
                                      }
                                      alt="User"
                                      className="w-6 h-6 rounded-full mr-2"
                                    />
                                    <span>{member.full_name}</span>
                                  </div>
                                </td>
                                <td className="px-3 py-[10px] text-[14px] text-left">
                                  {member.general_contact}
                                </td>
                                <td className="px-3 py-[10px] text-[14px] text-left">
                                  {member.member_type}
                                </td>
                                <td className="px-3 py-[10px] text-[14px] text-left">
                                  {member.general_email}
                                </td>
                                <td className="px-3 py-[10px] text-[14px] text-left">
                                  {role_name}
                                </td>
                              </tr>
                            ))
                          ) : (
                            <tr>
                              <td colSpan="5" className="text-center py-4">
                                No members assigned to this role
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoleProfile;
