import React, { useState, useCallback, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { X } from "lucide-react";

const acceptedFileTypes = [
  "image/png",
  "image/jpeg",
  "image/jpg",
  "application/pdf"
];

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

const MultipleImageDropzoneResidentEdit = ({ onUpload, initialFiles = [], onRemove }) => {
  const [files, setFiles] = useState([]);
  const [error, setError] = useState("");

  // Initialize with existing files
  useEffect(() => {
    if (initialFiles && initialFiles.length > 0) {
      setFiles(
        initialFiles.map((file) => ({
          ...file,
          preview: file.isExisting
            ? file.url
            : file.type?.startsWith("image/")
            ? URL.createObjectURL(file)
            : null,
          // Ensure type is set for existing files if missing
          type: file.type || (file.url?.match(/\.(jpeg|jpg|png)$/i) ? "image/jpeg" : "application/pdf"),
        }))
      );
    }
  }, [initialFiles]);

  const onDrop = useCallback(
    (acceptedFiles) => {
      // Validate file types
      const validTypeFiles = acceptedFiles.filter((file) =>
        acceptedFileTypes.includes(file.type)
      );
      if (validTypeFiles.length !== acceptedFiles.length) {
        setError("Only PDF, PNG, JPG, and JPEG files are allowed.");
        return;
      }

      // Validate file sizes
      const sizeValidFiles = validTypeFiles.filter((file) => file.size <= MAX_FILE_SIZE);
      if (sizeValidFiles.length !== validTypeFiles.length) {
        setError("Files larger than 5MB are not allowed.");
        return;
      }

      setError(""); // Clear any previous errors

      // Create preview for images (and placeholder for PDFs)
      const newFiles = sizeValidFiles.map((file) =>
        Object.assign(file, {
          preview: file.type === "application/pdf" ? null : URL.createObjectURL(file),
          isExisting: false,
        })
      );

      setFiles((prev) => [...prev, ...newFiles]);
      onUpload(newFiles);
    },
    [onUpload]
  );

  const removeFile = useCallback(
    (index) => {
      const fileToRemove = files[index];
      setFiles((prev) => prev.filter((_, i) => i !== index));

      if (onRemove) {
        onRemove(fileToRemove);
      }

      if (!fileToRemove.isExisting && fileToRemove.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
    },
    [files, onRemove]
  );

  const { getRootProps, getInputProps } = useDropzone({
    accept: acceptedFileTypes.join(","),
    onDrop,
    multiple: true,
  });

  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      files.forEach((file) => {
        if (!file.isExisting && file.preview) {
          URL.revokeObjectURL(file.preview);
        }
      });
    };
  }, [files]);

  return (
    <div className="p-4 border border-dashed rounded-lg">
      <div
        {...getRootProps()}
        className="border border-dashed border-gray-400 p-6 text-center cursor-pointer"
      >
        <input {...getInputProps()} />
        <p className="text-gray-600">Drag & drop files here, or click to select</p>
        <p className="text-sm text-gray-500 mt-1">
          (Supports: PDF, PNG, JPG, JPEG; max 5MB each)
        </p>
      </div>

      {error && <p className="text-red-500 mt-2">{error}</p>}

      <div className="mt-4 grid grid-cols-3 gap-4">
        {files.map((file, index) => (
          <div key={file.id || file.name || index} className="relative">
            {(file.type?.startsWith("image/") && file.preview) ? (
              <img
                src={file.preview}
                alt="Preview"
                className="w-full h-32 object-cover rounded-md"
              />
            ) : (
              <div className="w-full h-32 flex items-center justify-center bg-gray-200 rounded-md">
                <p className="text-sm text-gray-600">PDF File</p>
              </div>
            )}
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                removeFile(index);
              }}
              className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1"
            >
              <X size={16} />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MultipleImageDropzoneResidentEdit;
