import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { unitDetails } from "../../../../redux/slices/units/unitSlice";
import EditButton from "../../../../Components/Buttons/EditButton";
import FilePreviewWithDownload from "./FilePreviewWithDownload";

const UnitInformationTab = ({ id }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { selectedUnitDetails } = useSelector((state) => state.unit);
  useEffect(() => {
    if (id) {
      dispatch(unitDetails(id));
    }
  }, [id, dispatch]);

  const formatValue = (value) =>
    value !== null && value !== undefined && value !== "" ? value : "---";

  const handleNavigate = () => {
    navigate(`/add-unit/${id}`);
  };
  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-teal-600">General Information</h2>
        <EditButton
          onClick={handleNavigate}
          className="flex items-center px-4 py-2 border border-gray-300 rounded-md "
        >
          <img src="./edit-02.png" alt="Edit" className="w-4 h-4 mr-2" />
          <span className="text-sm text-gray-700">Edit</span>
        </EditButton>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        <div className="w-full lg:w-3/4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="space-y-4">
              <div>
                <p className="text-sm text-grey100">Area</p>
                <p className="text-base font-medium">
                  {formatValue(selectedUnitDetails?.area)} sq. ft.
                </p>
              </div>
              <div>
                <p className="text-sm text-grey100">Number of Bathrooms</p>
                <p className="text-base font-medium">
                  {formatValue(selectedUnitDetails?.number_of_bathrooms)}
                </p>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-grey100">Number of Rooms</p>
                <p className="text-base font-medium">
                  {formatValue(selectedUnitDetails?.number_of_rooms)}
                </p>
              </div>
              <div>
                <p className="text-sm text-grey100">Number of Balconies</p>
                <p className="text-base font-medium">
                  {formatValue(selectedUnitDetails?.number_of_balconies)}
                </p>
              </div>
            </div>
          </div>

          {["Primary", "Secondary", "Emergency"].map((type) => (
            <div key={type} className="mb-8">
              <h2 className="text-lg font-bold text-primary mb-4">
                {type} Contact
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-grey100">Name</p>
                    <p className="text-base font-medium">
                      {formatValue(
                        selectedUnitDetails?.[`${type.toLowerCase()}_name`]
                      )}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-grey100">Number</p>
                    <p className="text-base font-medium">
                      {formatValue(
                        selectedUnitDetails?.[`${type.toLowerCase()}_number`]
                      )}
                    </p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="">
                    <p className="text-sm text-grey100">Relationship</p>
                    <p className="text-base font-medium">
                      {formatValue(
                        selectedUnitDetails?.[
                          `${type.toLowerCase()}_relationship`
                        ]
                      )}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-grey100">Email</p>
                    <p className="text-base font-medium">
                      {formatValue(
                        selectedUnitDetails?.[`${type.toLowerCase()}_email`]
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="flex flex-row gap-2 overflow-x-auto">
          {selectedUnitDetails?.docs?.length > 0 ? (
            selectedUnitDetails.docs.map((doc) => {
              const filePath = doc.unit_docs; // Should be a relative path
              return (
                <FilePreviewWithDownload key={doc.id} filePath={filePath} />
              );
            })
          ) : (
            <p className="text-sm text-gray-500">No documents available</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default UnitInformationTab;
