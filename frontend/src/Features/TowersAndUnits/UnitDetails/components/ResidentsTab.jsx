import React, { useState, useEffect } from "react";
import { <PERSON>a<PERSON><PERSON>, FaPlus } from "react-icons/fa";
import Button from "../../../../Components/FormComponent/ButtonComponent/Button";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteResidents,
  fetchResidents,
} from "../../../../redux/slices/residents/residentSlice";
import axios from "axios";
import user from "../../../../assets/user/user.png";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";
import { fetchOwnerList } from "../../../../redux/slices/owner/ownerSlice";
import { setActiveTabs } from "../../../../redux/slices/memberSlice";
import { checkPermission } from "../../../../utils/permissionUtils";

const api = axios.create({
  baseURL: import.meta.env.VITE_BASE_API,
});

const ResidentsTab = ({ unitId }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Permission state
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);

  // Local UI state
  const [selectedStatus, setSelectedStatus] = useState("Vacant");
  const [selectedResidents, setSelectedResidents] = useState([]);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  // Redux state
  const {
    residents,
    loadingResidentsList,
    errorResidentsList,
    loadingDelete,
  } = useSelector((state) => state.resident);
  const { ownerList } = useSelector((state) => state.owner);

  const hasOwner = ownerList?.owners?.length > 0;
  const isVacant = residents.length === 0;

  useEffect(() => {
    if (unitId) {
      dispatch(fetchResidents(unitId));
      dispatch(fetchOwnerList(unitId));
    }
  }, [dispatch, unitId]);

  useEffect(() => {
    if (!loadingResidentsList) {
      setSelectedStatus(isVacant ? "Vacant" : "Occupied");
    }
  }, [isVacant, loadingResidentsList]);


  useEffect(() => {
    const fetchPermission = async () => {
 
      const permissionGranted = await checkPermission("org", 18);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  const handleRemoveResidents = () => {
    if (selectedResidents.length === 0) return;

  // Clear old messages
  setSuccessMessage("");
  setErrorMessage("");

  dispatch(deleteResidents({selectedResidents, unitId}))
    .unwrap()
    .then((response) => {
      dispatch(fetchResidents(unitId));
      setSelectedResidents([]);
      setSuccessMessage(response.message || "Residents removed successfully");
    })
    .catch((error) => {
      // Show backend error message or fallback to generic message
      setErrorMessage(error?.message || "Failed to remove residents");
    });
};

  const clearMessage = () => {
    setSuccessMessage("");
    setErrorMessage("");
  };

  if (loadingPermission) return <div className="p-4">Loading...</div>;
  if (!hasPermission) {
    navigate("/not-authorized");
    return null;
  }

  if (loadingResidentsList) return <LoadingAnimation />;

  if (errorResidentsList) return <p className="text-red-500">{errorResidentsList}</p>;

  return (
    <div className="relative">
      {/* Success/Error MessageBox */}
      {(successMessage || errorMessage) && (
        <MessageBox
          message={successMessage}
          error={errorMessage}
          onOk={clearMessage}
        />
      )}

      {!hasOwner && (
        <div className="text-center p-8 bg-gray-50 rounded-lg mb-4">
          <p className="text-gray-600 mb-4">This unit has no owner assigned yet.</p>
          <p className="text-gray-500">Please assign an owner first to manage residents.</p>
        </div>
      )}

      {/* Status selection section */}
      <div className="mr-[10px] mb-2">
        <div>
          <h2>Status</h2>
        </div>
        <div className="flex items-center gap-2">
          {["Vacant", "Occupied"].map((status) => (
            <div key={status} className="flex items-center gap-2">
              <input
                type="radio"
                name="status"
                value={status}
                checked={selectedStatus === status}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-6 h-6 px-2 accent-primary"
                disabled={!hasOwner}
              />
              <label>{status}</label>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold text-[#3C9D9B]">Residents List</h2>
        <div className="flex gap-2">
          <Button
            disabled={!hasOwner || isVacant || selectedResidents.length === 0 || loadingDelete}
            size="small"
            variant="red"
            onClick={handleRemoveResidents}
          >
            Remove
          </Button>
          <button
            disabled={!hasOwner || selectedStatus === "Vacant"}
            onClick={() => navigate(`/addResident/${unitId}`)}
            className="flex items-center bg-primary text-white p-2 rounded"
          >
            <FaPlus className="mr-2" /> Add Resident
          </button>
        </div>
      </div>

      <div className="overflow-y-auto max-h-[400px]">
        <table className="w-full text-sm text-left">
          <thead className="bg-[#3C9D9B1A]">
            <tr>
              <th className="px-2 py-2">
                <input
                  className="mr-3 accent-primary w-5 h-5"
                  type="checkbox"
                  checked={selectedResidents.length === residents.length && !isVacant}
                  onChange={(e) =>
                    setSelectedResidents(e.target.checked ? residents.map((r) => r.id) : [])
                  }
                  disabled={isVacant}
                />
              </th>
              <th className="px-2 py-2">Name</th>
              <th className="px-2 py-2">Contact</th>
              <th className="px-2 py-2">Email</th>
              <th className="px-2 py-2">Type</th>
              <th className="px-2 py-2">Action</th>
            </tr>
          </thead>
          <tbody>
            {isVacant ? (
              <tr>
                <td colSpan="6" className="text-center py-4">
                  No residents to display.
                </td>
              </tr>
            ) : (
              residents.map((r) => (
                <tr key={r.id} className="bg-white border-b hover:bg-gray-50">
                  <td className="px-2 py-2">
                    <input
                      type="checkbox"
                      className="mr-3 accent-primary w-5 h-5"
                      checked={selectedResidents.includes(r.id)}
                      onChange={(e) =>
                        setSelectedResidents((prev) =>
                          e.target.checked
                            ? [...prev, r.id]
                            : prev.filter((id) => id !== r.id)
                        )
                      }
                    />
                  </td>
                  <td className="px-2 py-2 flex items-center">
                    <img
                      src={
                        r.member.photo_low_quality
                          ? `${api.defaults.baseURL}${r.member.photo_low_quality}`
                          : user
                      }
                      alt=""
                      className="w-8 h-8 rounded-full mr-2"
                    />
                    {r.member.full_name}
                  </td>
                  <td className="px-2 py-2">{r.member.general_contact}</td>
                  <td className="px-2 py-2">{r.member.general_email}</td>
                  <td className="px-2 py-2">{r.resident_type}</td>
                  <td className="px-2 py-2">
                    <Link
                      to={`/member-profile/${r.member.id}`}
                      onClick={() => dispatch(setActiveTabs(1))}
                    >
                      <FaEye className="w-[25px] h-[20px] text-primary" />
                    </Link>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {loadingDelete && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50">
          <LoadingAnimation />
        </div>
      )}
    </div>
  );
};

export default ResidentsTab;