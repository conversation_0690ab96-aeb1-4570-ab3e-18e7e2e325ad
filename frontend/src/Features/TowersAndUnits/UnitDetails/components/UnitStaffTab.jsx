import React, { useState, useEffect } from "react";
import { <PERSON>a<PERSON><PERSON>, FaPlus } from "react-icons/fa";
import Button from "../../../../Components/FormComponent/ButtonComponent/Button";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchUnitStaff,
  resetUnitStaffState,
  bulkDeleteUnitStaff
} from "../../../../redux/slices/unitStaff/unitStaffSlice";
import user from "../../../../assets/user/user.png";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";
import { setActiveTabs } from "../../../../redux/slices/memberSlice";
import axios from "axios";
import { checkPermission } from "../../../../utils/permissionUtils";

const api = axios.create({
  baseURL: import.meta.env.VITE_BASE_API
});
const UnitStaffTab = ({ unitId }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [selectedStaff, setSelectedStaff] = useState([]);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  const {
    residents: staffList,
    loadingUnitStaffList,
    errorUnitStaffsList,
    loading
  } = useSelector((state) => state.unitStaff);

  // Permission state
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);

  useEffect(() => {
    const fetchPermission = async () => {
    
      const permissionGranted = await checkPermission("org", 21);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  useEffect(() => {
    if (unitId) {
      dispatch(fetchUnitStaff(unitId));
    }
    return () => {
      dispatch(resetUnitStaffState());
    };
  }, [dispatch, unitId]);

  const handleRemoveStaff = () => {
    if (!selectedStaff.length) return;

    setSuccessMessage("");
    setErrorMessage("");
    dispatch(bulkDeleteUnitStaff(selectedStaff))
      .unwrap()
      .then(() => {
        dispatch(fetchUnitStaff(unitId));
        setSelectedStaff([]);
        setSuccessMessage("Staff removed successfully");
      })
      .catch(() => {
        setErrorMessage("Failed to remove staff");
      });
  };

  const clearMessage = () => {
    setSuccessMessage("");
    setErrorMessage("");
  };

  if (loadingPermission) return <LoadingAnimation />;
  if (!hasPermission) {
    navigate("/not-authorized");
    return null;
  }
  if (loadingUnitStaffList) return <LoadingAnimation />;
  if (errorUnitStaffsList)
    return <p className="text-red-500">{errorUnitStaffsList}</p>;

  return (
    <div className="relative">
      {(successMessage || errorMessage) && (
        <MessageBox
          message={successMessage}
          error={errorMessage}
          onOk={clearMessage}
        />
      )}

      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-50">
          <LoadingAnimation />
        </div>
      )}

      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold text-[#3C9D9B]">Unit Staff List</h2>
        <div className="flex gap-2">
          <Button
            disabled={!selectedStaff.length || loading}
            size="small"
            variant="red"
            onClick={handleRemoveStaff}
          >
            {loading ? "Removing…" : "Remove"}
          </Button>
          <button
            onClick={() => {
              dispatch(resetUnitStaffState());
              navigate(`/addUnitStaff/${unitId}`);
            }}
            className="flex items-center bg-primary text-white p-2 rounded"
          >
            <FaPlus className="mr-2" /> Add Unit Staff
          </button>
        </div>
      </div>

      <div className="overflow-y-auto max-h-[400px]">
        <table className="w-full text-sm text-left">
          <thead className="bg-[#3C9D9B1A] shadow-lg">
            <tr>
              <th className="px-2 py-2">
                <input
                  type="checkbox"
                                            className="mr-3 accent-primary w-5 h-5"

                  checked={
                    selectedStaff.length === staffList.length &&
                    staffList.length > 0
                  }
                  onChange={(e) =>
                    setSelectedStaff(
                      e.target.checked ? staffList.map((s) => s.id) : []
                    )
                  }
                />
              </th>
              <th className="px-2 py-2">Name</th>
              <th className="px-2 py-2">Contact</th>
              <th className="px-2 py-2">Email</th>
              <th className="px-2 py-2">Occupation</th>
              <th className="px-2 py-2">Action</th>
            </tr>
          </thead>
          <tbody>
            {!staffList.length ? (
              <tr>
                <td colSpan="6" className="text-center py-4">
                  No unit staff found.
                </td>
              </tr>
            ) : (
              staffList.map((s) => (
                <tr key={s.id} className="bg-white border-b hover:bg-gray-50">
                  <td className="px-2 py-2">
                    <input
                      type="checkbox"
                                                className="mr-3 accent-primary w-5 h-5"

                      checked={selectedStaff.includes(s.id)}
                      onChange={(e) =>
                        setSelectedStaff((prev) =>
                          e.target.checked
                            ? [...prev, s.id]
                            : prev.filter((id) => id !== s.id)
                        )
                      }
                    />
                  </td>
                  <td className="px-2 py-2 flex items-center">
                    <img
                      src={
                        s.member?.photo_low_quality
                          ? `${api.defaults.baseURL}${s.member.photo_low_quality}`
                          : user
                      }
                      alt=""
                      className="w-8 h-8 rounded-full mr-2"
                    />
                    {s.member?.full_name || "N/A"}
                  </td>
                  <td className="px-2 py-2">
                    {s.member?.general_contact || "N/A"}
                  </td>
                  <td className="px-2 py-2">
                    {s.member?.general_email || "N/A"}
                  </td>
                  <td className="px-2 py-2">{s.member?.occupation || "N/A"}</td>
                  <td className="px-2 py-2">
                    <Link
                      to={`/member-profile/${s.member?.id}`}
                      onClick={() => dispatch(setActiveTabs(1))}
                    >
                      <FaEye className="w-[25px] h-[20px] text-primary" />
                    </Link>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default UnitStaffTab;
