import { useEffect, useState } from "react";
import { FaRegClock } from "react-icons/fa";
import { BiArrowBack } from "react-icons/bi";
import { useNavigate, useParams, useLocation, Link } from "react-router-dom";

import UnitInformationTab from "../components/UnitInformationTab";
import UnitOwnerTab from "../components/UnitOwnerTab";
import ResidentsTab from "../components/ResidentsTab";
import UnitStaffTab from "../components/UnitStaffTab";
import UnitTowerInfo from "../components/UnitTowerInfo";
import Button from "../../../../Components/FormComponent/ButtonComponent/Button";
import TableSkeleton from "../../../../Components/Loaders/TableSkeleton";

import { checkPermission } from "../../../../utils/permissionUtils";

const UnitDetails = () => {
  const [activeTab, setActiveTab] = useState(1);
  const { id: unitId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();

  // Permission state
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);

  // Handle tab from query string like ?tab=2
  useEffect(() => {
    const query = new URLSearchParams(location.search);
    const tab = parseInt(query.get("tab"));
    if (tab && tab >= 1 && tab <= 4) {
      setActiveTab(tab);
    }
  }, [location.search]);

  // Check permission on mount
  useEffect(() => {
    const fetchPermission = async () => {
      const permissionGranted = await checkPermission("org", 13);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  // Redirect if permission denied
  useEffect(() => {
    if (!loadingPermission && !hasPermission) {
      navigate("/not-authorized");
    }
  }, [loadingPermission, hasPermission, navigate]);

  // Show loading while checking permission
  if (loadingPermission) {
    return (
      <div className="flex items-center justify-center my-12">
        <TableSkeleton />
      </div>
    );
  }

  // Don't render if no permission and navigation is pending
  if (!hasPermission) {
    return null;
  }

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    // Update URL with new tab
    navigate(`/unit-details/${unitId}?tab=${tabId}`, { replace: true });
  };

  const tabs = [
    {
      id: 1,
      name: "Unit Information",
      content: <UnitInformationTab id={unitId} />
    },
    {
      id: 2,
      name: "Unit Owner",
      content: <UnitOwnerTab unitId={unitId} />
    },
    {
      id: 3,
      name: "Residents",
      content: <ResidentsTab unitId={unitId} />
    },
    {
      id: 4,
      name: "Unit Staff",
      content: <UnitStaffTab unitId={unitId} />
    }
  ];

  return (
    <div className="p-4 h-full">
      <div className="container mx-auto">
        <div className="flex justify-between max-w-[1282px] py-4">
          <Link to="/ViewTowers">
            <p className="flex items-center text-2xl font-bold">
              <BiArrowBack className="mr-2 text-gray-600 cursor-pointer" />
              Unit Details
            </p>
          </Link>

          <Button
            size="small"
            className="flex items-center justify-between text-lg font-medium bg-primary text-white hover:bg-primary transition-all duration-300 p-2 rounded"
          >
            <FaRegClock className="mr-2" />
            History
          </Button>
        </div>

        <div className="flex flex-col md:flex-row rounded-xl max-w-[1282px] bg-white">
          <UnitTowerInfo id={unitId} />

          <div className="w-full md:w-3/4 p-6">
            <div className="flex mb-4 bg-subprimary">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`flex-1 py-2 font-semibold text-center ${
                    activeTab === tab.id
                      ? "border-b-2 border-primary text-primary"
                      : "border-b-2 border-transparent hover:border-primary hover:text-primary"
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </div>

            <div className="mt-6">
              {tabs.find((tab) => tab.id === activeTab)?.content}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnitDetails;
