import React, { useEffect, useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { BiArrowBack } from "react-icons/bi";
import { FaPlus } from "react-icons/fa6";

import {
  fetchTowers,
  deleteTower,
  clearMessages
} from "../../../../redux/slices/towers/towerSlice";

import { checkPermission } from "../../../../utils/permissionUtils";
import Button from "../../../../Components/FormComponent/ButtonComponent/Button";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import ConfirmationMessageBox from "../../../../Components/MessageBox/ConfirmationMessageBox";
import TableSkeleton from "../../../../Components/Loaders/TableSkeleton";
import TowerCard from "../components/TowerCard";
import { useUnitStatus } from "../hooks/useUnitStatus";
import Heading from "../../../../Components/HeadingComponent/Heading";

const ViewTowers = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { towers, loading, error, successMessage } = useSelector(
    (state) => state.tower
  );
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [towerIdToDelete, setTowerIdToDelete] = useState(null);

  // const unitStatusMap =towers;

  // Permission check
  useEffect(() => {
    const fetchPermission = async () => {
      const permissionGranted = await checkPermission("org", 12);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  // Fetch towers
  useEffect(() => {
    dispatch(fetchTowers());
  }, [dispatch]);

  // Memoized handlers
  const handleDeleteClick = useCallback((towerId) => {
    setTowerIdToDelete(towerId);
    setShowConfirmation(true);
  }, []);

  const handleConfirmDelete = useCallback(() => {
    if (!towerIdToDelete) return;
    dispatch(deleteTower(towerIdToDelete)).then(() => {
      setShowConfirmation(false);
      setTowerIdToDelete(null);
    });
  }, [dispatch, towerIdToDelete]);

  const handleCancelDelete = useCallback(() => {
    setShowConfirmation(false);
    setTowerIdToDelete(null);
  }, []);

  const handleMessageBoxOk = useCallback(() => {
    dispatch(clearMessages());
    dispatch(fetchTowers());
  }, [dispatch]);

  if (loadingPermission) {
    return (
      <div className="flex items-center justify-center my-12">
        <TableSkeleton />
      </div>
    );
  }

  if (!hasPermission) {
    navigate("/not-authorized");
    return null;
  }

  return (
    <div className="container mx-auto px-5 ">
      <div className=" h-full">
        <div className="container">
          <div className="md:flex justify-between  py-4">
            <Heading title=" Tower List" size="xl" color="text-black" />

            <Button icon={FaPlus} onClick={() => navigate("/addTower")}>
              Add Tower
            </Button>
          </div>

          {loading ? (
            <div className="flex items-center justify-center my-12">
              <TableSkeleton />
            </div>
          ) : towers.length === 0 ? (
            <div className="flex flex-col items-center justify-center my-12 text-gray-500">
              <p className="text-xl font-medium mb-2">No Towers Found</p>
              <p className="text-sm">
                No tower has been added to the system yet.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {towers.map((tower) => (
                <TowerCard
                  key={tower.id}
                  tower={tower}
                  // unitStatusMap={unitStatusMap}
                  onDeleteClick={handleDeleteClick}
                />
              ))}
            </div>
          )}

          {showConfirmation && (
            <ConfirmationMessageBox
              message="Are you sure you want to delete this tower?"
              onConfirm={handleConfirmDelete}
              onCancel={handleCancelDelete}
            />
          )}

          {(error || successMessage) && (
            <MessageBox
              message={successMessage}
              error={error}
              onOk={handleMessageBoxOk}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default ViewTowers;
