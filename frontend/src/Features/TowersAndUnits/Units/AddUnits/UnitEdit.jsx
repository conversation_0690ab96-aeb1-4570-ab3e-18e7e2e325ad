import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { BiArrowBack } from "react-icons/bi";
import { FaRegClock } from "react-icons/fa";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

import Button from "../../../../Components/FormComponent/ButtonComponent/Button";
import UnitTowerInfo from "../../UnitDetails/components/UnitTowerInfo";
import {
  unitDetails,
  addExistingContact,
  unitUpdate
} from "../../../../redux/slices/units/unitSlice";
import ComMemberTable from "./ComMemberTable";
import MessageBox from "../../../../Components/MessageBox/MessageBox";

import UnitEditForm from "./UnitEditForm";
import { checkPermission } from "utils/permissionUtils";
import TableSkeleton from "Components/Loaders/TableSkeleton";
 

const UnitEdit = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
     const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);

  const { selectedUnitDetails, memberContact } = useSelector(
    (state) => state.unit
  );
  const [initialFiles, setInitialFiles] = useState([]);

  const [uploadedImages, setUploadedImages] = useState([]);
  const [filesToRemove, setFilesToRemove] = useState([]);
  const [showSuccess, setShowSuccess] = useState(false);
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [contactType, setContactType] = useState(null);
  const [filesDirty, setFilesDirty] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    // formState: { errors },
    reset,
    formState: { errors, isDirty }
  } = useForm({
    defaultValues: {
      area: 0,
      number_of_rooms: 0,
      number_of_bathrooms: 0,
      number_of_balconies: 0
    }
  });
 

  useEffect(() => {
    const fetchPermission = async () => {
      const permissionGranted = await checkPermission("org", 14);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  useEffect(() => {
    if (!loadingPermission && !hasPermission) {
      navigate("/not-authorized");
    }
  }, [loadingPermission, hasPermission, navigate]);

  useEffect(() => {
    if (id) {
      dispatch(unitDetails(id));
      dispatch(addExistingContact(id));
    }
  }, [id, dispatch]);

  // Existing useEffect for initial data load
  useEffect(() => {
    if (selectedUnitDetails && selectedUnitDetails.id) {
      reset({
        area: selectedUnitDetails.area || 0,
        number_of_rooms: selectedUnitDetails.number_of_rooms || 0,
        number_of_bathrooms: selectedUnitDetails.number_of_bathrooms || 0,
        number_of_balconies: selectedUnitDetails.number_of_balconies || 0,
        primary_name: selectedUnitDetails.primary_name,
        primary_number: selectedUnitDetails.primary_number,
        primary_email: selectedUnitDetails.primary_email,
        primary_relationship: selectedUnitDetails.primary_relationship,
        secondary_name: selectedUnitDetails.secondary_name,
        secondary_number: selectedUnitDetails.secondary_number,
        secondary_email: selectedUnitDetails.secondary_email,
        secondary_relationship: selectedUnitDetails.secondary_relationship,
        emergency_name: selectedUnitDetails.emergency_name,
        emergency_number: selectedUnitDetails.emergency_number,
        emergency_email: selectedUnitDetails.emergency_email,
        emergency_relationship: selectedUnitDetails.emergency_relationship
      });
      const existing = selectedUnitDetails.docs || [];
      setUploadedImages(existing);
      setInitialFiles(existing); // Set initial files here
      setFilesDirty(false);
    }
  }, [selectedUnitDetails, reset]);

  useEffect(() => {
    const hasChanges = !(
      initialFiles.length === uploadedImages.length &&
      initialFiles.every((initialFile, index) => {
        const currentFile = uploadedImages[index];
        if (!currentFile) return false;

        // Compare backend files by ID
        if (initialFile.isFromBackend && currentFile.isFromBackend) {
          return initialFile.id === currentFile.id;
        }

        // Compare new files by name and size
        return (
          initialFile.name === currentFile.name &&
          initialFile.size === currentFile.size
        );
      })
    );

    setFilesDirty(hasChanges);
  }, [uploadedImages, initialFiles]);

  const handleContactSelect = (contact) => {
    if (contactType === "primary") {
      setValue("primary_name", contact.full_name, { shouldDirty: true });
      setValue("primary_number", contact.general_contact, {
        shouldDirty: true
      });
      setValue("primary_email", contact.general_email, { shouldDirty: true });
    } else if (contactType === "secondary") {
      setValue("secondary_name", contact.full_name, { shouldDirty: true });
      setValue("secondary_number", contact.general_contact, {
        shouldDirty: true
      });
      setValue("secondary_email", contact.general_email, { shouldDirty: true });
    }
    setShowModal(false);
  };

  const onSubmit = async (data) => {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, value);
      }
    });

    uploadedImages.forEach((file) => {
      if (file.isFromBackend) {
        formData.append("existing_data", file.id);
      } else if (file instanceof Blob) {
        formData.append("docs", file, file.name);
      }
    });

    filesToRemove.forEach((id) => {
      formData.append("filesToRemove", id);
    });

    try {
      await dispatch(unitUpdate({ id, data: formData })).unwrap();
      setMessage("Unit Update successful");
      setShowSuccess(true);
    } catch (err) {
      setError(err.message || "Failed to update unit.");
    }
  };

  const handleUpload = (newFiles = [], removedFiles = [], removedIds = []) => {
    setUploadedImages((prev) => {
      const updated = prev.filter(
        (file) =>
          !removedIds.includes(file.id) &&
          !removedFiles.some(
            (f) =>
              !file.isFromBackend &&
              f.name === file.name &&
              f.size === file.size
          )
      );

      const filesToAdd = newFiles.filter(
        (newFile) =>
          !updated.some(
            (existing) =>
              (newFile.isFromBackend &&
                existing.isFromBackend &&
                newFile.id === existing.id) ||
              (!newFile.isFromBackend &&
                !existing.isFromBackend &&
                newFile.name === existing.name &&
                newFile.size === existing.size)
          )
      );

      return [...updated, ...filesToAdd];
    });

    // mark dirty when files change
    if (newFiles.length > 0 || removedIds.length > 0) {
      setFilesDirty(true);
    }

    setFilesToRemove((prev) => [...new Set([...prev, ...removedIds])]);
  };

  const handleOk = () => {
    navigate(`/unit-details/${id}`);
  };
  const combinedDirty = isDirty || filesDirty;

  if (loadingPermission) {
    return (
      <div className="flex justify-center items-center h-screen">
        <TableSkeleton />
      </div>
    );
  }

  if (!hasPermission) return null;

  return (
    <div className=" my-3 ">
      <div className="container">
        <div className="md:flex justify-between max-w-[1250px] py-[14px]">
          <Link to={`/unit-details/${id}`}>
            <p className="flex items-center justify-between font-medium text-[24px]">
              <BiArrowBack className="mr-2 text-gray-600 cursor-pointer" />
              Unit details
            </p>
          </Link>
          <Button
            size="small"
            className="flex items-center text-lg font-medium bg-primary text-white transition-all duration-300 p-2 rounded"
          >
            <FaRegClock className="mr-2" />
            History
          </Button>
        </div>

        <ComMemberTable
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          contactType={contactType}
          commMembers={memberContact}
          onSelect={handleContactSelect}
        />

        <MessageBox
          message={message}
          error={error}
          clearMessage={() => setShowSuccess(false)}
          onOk={handleOk}
        />

        <div className="bg-white border px-base max-w-[1250px] border-white rounded-[27px]">
          <div className="md:flex md:grid-cols-2">
            <UnitTowerInfo id={id} />
            <UnitEditForm
              selectedUnitDetails={selectedUnitDetails}
              onSubmit={onSubmit}
              handleUpload={handleUpload}
              contactType={contactType}
              setContactType={setContactType}
              showModal={showModal}
              setShowModal={setShowModal}
              memberContact={memberContact}
              handleContactSelect={handleContactSelect}
              uploadedImages={uploadedImages}
              register={register}
              handleSubmit={handleSubmit}
              setValue={setValue}
              errors={errors}
              isDirty={combinedDirty}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnitEdit;
