import React, { useEffect, useState, useRef } from "react";
import { useForm, useField<PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

import {
  createOwner,
  clearCreatedMember,
  clearMessage,
  fetchOwnerList
} from "../../../../redux/slices/owner/ownerSlice";

import {
  getOrdinal,
  formatDate,
  ownerValidationSchema
} from "../utils/ownerUtils";

import { Paragraph } from "../../../../Components/Ui/Paragraph";
import NumberInputComponent from "../../../../Components/FormComponent/NumberInputComponent";
import FileDropzone from "../Components/FileDropzone";
import { FaMinus, FaPlus } from "react-icons/fa";
import AddMemberForm from "../Components/Modals/AddMemberForm";
import AddCompany from "../AddCompany/AddCompany";
import MemberSearchAutocomplete from "../../../../Components/MemberSearchAutocomplete/MemberSearchAutocomplete";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import { setCreatedMember } from "../../../../redux/slices/owner/ownerSlice";
import { clearCreatedCompany } from "../../../../redux/slices/companySlice";

const AddOwnerForm = ({ unitId }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const ownerState = useSelector((state) => state.owner);
  const [isMemberModalOpen, setIsMemberModalOpen] = useState(false);
  const [showCompanyModal, setShowCompanyModal] = useState(false);
  const [searchTerms, setSearchTerms] = useState({});
  const companyData = useSelector((state) => state.company.company_data);
  const [localError, setLocalError] = useState(null);
  const [isFormChanged, setIsFormChanged] = useState(false);

  // Clear messages when component mounts and unmounts
  useEffect(() => {
    dispatch(clearMessage());
    return () => {
      dispatch(clearMessage());
    };
  }, [dispatch]);

  // Fetch existing owners when component mounts
  useEffect(() => {
    dispatch(fetchOwnerList(unitId));
  }, [dispatch, unitId]);

  // Check if owners exist and show message
  useEffect(() => {
    if (ownerState.ownerList?.owners?.length > 0) {
      setLocalError("Cannot add new owners. Please go to Change Ownership page to modify owners.");
    }
  }, [ownerState.ownerList]);

  const {
    register,
    control,
    handleSubmit,
    setValue,
    setError,
    clearErrors,
    formState: { errors },
    watch
  } = useForm({
    resolver: yupResolver(ownerValidationSchema),
    defaultValues: {
      owners: [
        {
          memberId: "",
          ownershipPercentage: "",
          dateofOwnership: "",
          document: []
        }
      ]
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "owners"
  });

  const ownerRefs = useRef([]);
  useEffect(() => {
    if (companyData?.data?.member && companyData?.data?.company_name) {
      // console.log("add owner aaaaaaaaaaaa", companyData);

      dispatch(
        setCreatedMember({
          id: companyData.data.member,
          full_name: companyData.data.company_name
        })
      );
    }
  }, [companyData]);

  useEffect(() => {
    if (ownerState.createdMember) {
      // Check if member already exists in any ownership block
      const existingOwnerIndex = fields.findIndex(
        (_, index) =>
          watch(`owners.${index}.memberId`) === ownerState.createdMember.id
      );

      if (existingOwnerIndex !== -1) {
        // Option 1: Show error message
        setLocalError("This owner has already been added.");
        dispatch(clearCreatedMember());
        dispatch(clearMessage());
        setIsMemberModalOpen(false);
        return;
      }

      const emptyIndex = fields.findIndex(
        (_, index) => !watch(`owners.${index}.memberId`)
      );
      const indexToUse = emptyIndex !== -1 ? emptyIndex : fields.length;

      if (emptyIndex === -1) {
        append({
          memberId: "",
          ownershipPercentage: "",
          dateofOwnership: "",
          document: []
        });
      }

      setTimeout(() => {
        setValue(`owners.${indexToUse}.memberId`, ownerState.createdMember.id);
        setSearchTerms((prev) => ({
          ...prev,
          [indexToUse]: ownerState.createdMember.full_name
        }));

        ownerRefs.current[indexToUse]?.current?.scrollIntoView({
          behavior: "smooth"
        });
        ownerRefs.current[indexToUse]?.current?.querySelector("input")?.focus();

        dispatch(clearCreatedMember());
        dispatch(clearCreatedCompany());
        setIsMemberModalOpen(false);
      }, 100);
    }
  }, [ownerState.createdMember, fields, watch, setValue, append, dispatch]);
  const onSubmit = (data) => {
    if (errors.owners?.message) return;

    // Check if any owners exist
    if (ownerState.ownerList?.owners?.length > 0) {
      setLocalError("Cannot add new owners. Please go to Change Ownership page to modify owners.");
      return;
    }

    // Check for duplicate members and merge their ownership percentages
    const memberOwnershipMap = new Map();
    data.owners.forEach(owner => {
      const memberId = owner.memberId;
      const percentage = parseFloat(owner.ownershipPercentage || 0);

      if (memberOwnershipMap.has(memberId)) {
        // Merge ownership percentages for duplicate members
        const existingPercentage = memberOwnershipMap.get(memberId);
        memberOwnershipMap.set(memberId, existingPercentage + percentage);
      } else {
        memberOwnershipMap.set(memberId, percentage);
      }
    });

    // Validate individual ownership percentages
    const invalidOwnership = Array.from(memberOwnershipMap.values()).some(percentage => {
      return percentage <= 0 || percentage > 100;
    });

    if (invalidOwnership) {
      setLocalError("Each ownership percentage must be greater than 0 and less than or equal to 100%");
      return;
    }

    // Calculate total ownership with precise decimal handling (2 decimal places)
    const totalOwnership = Array.from(memberOwnershipMap.values()).reduce((sum, percentage) => {
      return sum + percentage;
    }, 0);

    // Round to 2 decimal places for comparison
    const roundedTotal = parseFloat(totalOwnership.toFixed(2));

    // Check if total ownership is exactly 100%
    if (roundedTotal !== 100) {
      setLocalError(
        `Total ownership percentage must be exactly 100%. Current total: ${roundedTotal}%`
      );
      return;
    }

    // Create a new array with merged ownership percentages
    const mergedOwners = Array.from(memberOwnershipMap.entries()).map(([memberId, percentage]) => {
      const originalOwner = data.owners.find(owner => owner.memberId === memberId);
      return {
        ...originalOwner,
        ownershipPercentage: percentage.toString()
      };
    });

    // Submit the merged owners
    mergedOwners.forEach((owner) => {
      const formData = new FormData();
      formData.append("member", owner.memberId);
      formData.append("unit", parseInt(unitId, 10));
      formData.append(
        "ownership_percentage",
        parseFloat(owner.ownershipPercentage)
      );
      formData.append("date_of_ownership", formatDate(owner.dateofOwnership));

      // Handle file uploads
      if (owner.document && owner.document.length > 0) {
        owner.document.forEach((file) => {
          if (file instanceof File) {
            formData.append("owner_docs_upload", file);
          }
        });
      }

      console.log(`📦 Payload for Create Owner:`, {
        member: owner.memberId,
        unit: unitId,
        ownershipPercentage: owner.ownershipPercentage,
        date_of_ownership: formatDate(owner.dateofOwnership),
        documents: owner.document?.length || 0
      });

      dispatch(createOwner(formData));
    });
  };

  useEffect(() => {
    const subscription = watch((value) => {
      const hasChanged = value?.owners?.some((owner) => {
        return (
          owner.memberId ||
          owner.ownershipPercentage ||
          owner.dateofOwnership ||
          (owner.document && owner.document.length > 0)
        );
      });

      setIsFormChanged(hasChanged); // 🟢 Added for form change detection
    });

    return () => subscription.unsubscribe();
  }, [watch]);
  useEffect(() => {
    if (ownerState.successMessage) {
      setIsFormChanged(false); // 🟢 Reset form change detection after success
    }
  }, [ownerState.successMessage]);
  return (
    <>
      <AddCompany
        isOpen={showCompanyModal}
        onClose={() => setShowCompanyModal(false)}
        fields={fields}
      />

      {(localError || ownerState.successMessage || ownerState.error) && (
        <MessageBox
          message={localError || ownerState.successMessage || ownerState.error}
          error={!!(localError || ownerState.error)}
          clearMessage={() => {
            if (localError) {
              setLocalError(null);
            } else {
              dispatch(clearMessage());
            }
          }}
          onOk={() => {
            if (localError) {
              setLocalError(null);
              if (ownerState.ownerList?.owners?.length > 0) {
                navigate(`/unit/${unitId}/change-owner`);
              }
            } else {
              dispatch(clearMessage());
              if (ownerState.successMessage) {
                navigate(`/unit-details/${unitId}?tab=2`);
              }
            }
          }}
        />
      )}

      {!ownerState.ownerList?.owners?.length && (
        <form onSubmit={handleSubmit(onSubmit)} className="p-[20px]">
          <div className="flex flex-wrap gap-4 mb-6">
            <button
              type="button"
              className={`font-medium px-5 py-2.5 rounded-lg shadow ${fields.some((_, index) => !watch(`owners.${index}.memberId`))
                ? "bg-teal-600 hover:bg-teal-700 text-white"
                : "bg-gray-300 text-gray-500 cursor-not-allowed"
                }`}
              onClick={() => setIsMemberModalOpen(true)}
              disabled={!fields.some((_, index) => !watch(`owners.${index}.memberId`))}
            >
              + Add New Comm Member
            </button>
            <button
              type="button"
              className={`font-medium px-5 py-2.5 rounded-lg shadow ${fields.some((_, index) => !watch(`owners.${index}.memberId`))
                ? "bg-teal-600 hover:bg-teal-700 text-white"
                : "bg-gray-300 text-gray-500 cursor-not-allowed"
                }`}
              onClick={() => setShowCompanyModal(true)}
              disabled={!fields.some((_, index) => !watch(`owners.${index}.memberId`))}
            >
              + Add New Company
            </button>
          </div>

          {fields.map((field, index) => {
            const filePath = `owners.${index}.document`;
            const files = watch(filePath) || [];
            ownerRefs.current[index] =
              ownerRefs.current[index] || React.createRef();

            return (
              <div
                ref={ownerRefs.current[index]}
                key={field.id}
                className="relative bg-white border border-gray-200 rounded-2xl p-6 shadow-sm mb-6"
              >
                <div className="flex items-start justify-between">
                  <h4 className="text-base font-medium mb-4 text-green">
                    {getOrdinal(index + 1)} Ownership Details
                  </h4>
                  {index === 0 && (
                    <button
                      type="button"
                      onClick={() =>
                        append({
                          memberId: "",
                          ownershipPercentage: "",
                          dateofOwnership: "",
                          document: []
                        })
                      }
                      className="w-10 h-10 text-xl text-teal-600 font-bold rounded-full border border-teal-600 hover:bg-teal-50 flex items-center justify-center transition"
                    >
                      <FaPlus size={12} />
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 gap-3 lg:w-[687px]">
                  <div className="login-field">
                    <Paragraph className="my-3 text-sm font-medium">
                      Unit Owner Name*
                    </Paragraph>
                    <MemberSearchAutocomplete
                      value={searchTerms[index] || ""}
                      memberId={watch(`owners.${index}.memberId`)}
                      onSelect={(member) => {
                        setValue(`owners.${index}.memberId`, member.id);
                        setSearchTerms((prev) => ({
                          ...prev,
                          [index]: member.full_name
                        }));
                      }}
                      excludedMemberIds={fields
                        .filter((_, i) => i !== index) // Exclude current field itself
                        .map((_, i) => watch(`owners.${i}.memberId`))
                        .filter(Boolean)} // Only non-empty ids
                    />
                    {errors.owners?.[index]?.memberId && (
                      <p className="text-red-500 text-sm">
                        {errors.owners[index].memberId.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3 lg:w-[687px]">
                  <div className="login-field">
                    <Paragraph className="my-3 text-sm font-medium">
                      Ownership Percentage*
                    </Paragraph>
                    <Controller
                      name={`owners.${index}.ownershipPercentage`}
                      control={control}
                      render={({ field }) => (
                        <NumberInputComponent
                          {...field}
                          placeholder="%"
                          step="0.01"
                        />
                      )}
                    />
                    {errors.owners?.[index]?.ownershipPercentage && (
                      <p className="text-red-500 text-sm">
                        {errors.owners[index].ownershipPercentage.message}
                      </p>
                    )}
                  </div>

                  <div className="login-field">
                    <Paragraph className="my-3 text-sm font-medium">
                      Date of Ownership*
                    </Paragraph>
                    <input
                      type="date"
                      {...register(`owners.${index}.dateofOwnership`)}
                      className="w-full border px-3 py-2 rounded"
                    />

                    {errors.owners?.[index]?.dateofOwnership && (
                      <p className="text-red-500 text-sm">
                        {errors.owners[index].dateofOwnership.message}
                      </p>
                    )}
                  </div>

                  <div className="login-field col-span-2">
                    <Paragraph className="my-3 text-sm font-medium">
                      Upload Ownership Documents
                    </Paragraph>

                    <FileDropzone
                      files={files}
                      onDrop={(acceptedFiles) => {
                        const currentFiles = watch(filePath) || [];

                        if (currentFiles.length + acceptedFiles.length > 5) {
                          setError(`owners.${index}.document`, {
                            type: "manual",
                            message: "You can upload a maximum of 5 documents."
                          });
                          return;
                        }

                        setValue(filePath, [...currentFiles, ...acceptedFiles]);
                        clearErrors(`owners.${index}.document`);
                      }}
                      onRemove={(fileIndex) => {
                        const updated = files.filter((_, i) => i !== fileIndex);
                        setValue(filePath, updated);
                        if (updated.length <= 5) {
                          clearErrors(`owners.${index}.document`);
                        }
                      }}
                    />
                    {errors?.owners?.[index]?.document && (
                      <div className="text-red-500 text-sm mt-2">
                        {errors.owners[index].document.message}
                      </div>
                    )}
                  </div>
                </div>
                {fields.length > 1 && index !== 0 && (
                  <button
                    type="button"
                    className="absolute top-4 right-4 w-7 h-7 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 text-black"
                    onClick={() => remove(index)}
                  >
                    <FaMinus size={12} />
                  </button>
                )}
              </div>
            );
          })}

          {typeof errors.owners === "object" && "message" in errors.owners && (
            <p className="text-red-500 text-sm text-center mb-4">
              {errors.owners.message}
            </p>
          )}

          {typeof errors.owners === "string" && (
            <p className="text-red-500 text-sm text-center mb-4">
              {errors.owners}
            </p>
          )}

          <div className="mt-6">
            <button
              type="submit"
              className="w-full bg-teal-600 text-white py-2 px-4 rounded disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={ownerState.loading || !isFormChanged}
            >
              {ownerState.loading ? "Saving..." : "Save"}
            </button>

          </div>
        </form>
      )}

      <AddMemberForm
        isOpen={isMemberModalOpen}
        onClose={() => setIsMemberModalOpen(false)}
        unitId={unitId}
      />
    </>
  );
};

export default AddOwnerForm;
