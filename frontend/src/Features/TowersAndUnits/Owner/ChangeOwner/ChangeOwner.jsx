import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { BiArrowBack } from "react-icons/bi";
import UnitTowerInfo from "../../UnitDetails/components/UnitTowerInfo";
import { useDispatch } from "react-redux";
import { clearMessage, clearOwnerDetails } from "../../../../redux/slices/owner/ownerSlice";
import ChangeOwnerForm from "./ChangeOwnerForm";
import { setActiveTabs } from "../../../../redux/slices/memberSlice";
import { checkPermission } from "../../../../utils/permissionUtils";

const ChangeOwner = () => {
  const { unitId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Permission state
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);

  useEffect(() => {
    const fetchPermission = async () => {
      const permissionGranted = await checkPermission("org", 17);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  useEffect(() => {
    // Clear messages when component unmounts
    return () => {
      dispatch(clearMessage());
      dispatch(clearOwnerDetails());
    };
  }, [dispatch]);

  if (loadingPermission) return <div className="p-4">Loading...</div>;
  if (!hasPermission) {
    navigate("/not-authorized");
    return null;
  }

  return (
    <div className="h-full p-[14px]">
      <div className="container mx-auto">
        <div className="flex justify-between max-w-[1282px] py-4">
          <p className="flex items-center text-2xl font-medium">
            <BiArrowBack
              className="mr-2 text-gray-600 cursor-pointer"
              onClick={() => {
                dispatch(setActiveTabs(3));
                navigate(-1);
              }}
            />
            Change Ownership
          </p>
        </div>

        <div className="flex flex-col md:flex-row rounded-xl max-w-[1282px] bg-white">
          <UnitTowerInfo id={unitId} />
          <ChangeOwnerForm unitId={unitId} />
        </div>
      </div>
    </div>
  );
};

export default ChangeOwner;
