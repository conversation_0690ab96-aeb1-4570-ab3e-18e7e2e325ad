// src/hooks/useMemberValidation.js

import { useState } from "react";

const useMemberValidation = () => {
  const [errors, setErrors] = useState({});

  const validateForm = (formData, activeTab) => {
    const newErrors = {};

    //
    // Tab 1 validations (unchanged)
    //
    if (!formData.full_name) {
      newErrors.full_name = "Full name is required";
    }

    if (formData.nid_number) {
      if (
        !/^\d{10}$/.test(formData.nid_number) &&
        !/^\d{13}$/.test(formData.nid_number) &&
        !/^\d{17}$/.test(formData.nid_number)
      ) {
        newErrors.nid_number = "NID Number must be 10, 13, or 17 digits";
      }
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.general_email) {
      newErrors.general_email = "Email is required";
    } else if (!emailRegex.test(formData.general_email)) {
      newErrors.general_email = "Invalid email format";
    }

    const contactRegex = /^(018|019|013|017|015|016|014)\d{8}$/;
    if (!formData.general_contact) {
      newErrors.general_contact = "Contact Number is required";
    } else if (!contactRegex.test(formData.general_contact)) {
      newErrors.general_contact = "Invalid contact format";
    }

    //
    // Tab 2 validations (Login Credential)
    //
    if (activeTab === 2) {
      // Must pick a login method
      // if (!formData.login) {
      //   newErrors.login = "Please select login method";
      // } else {
      //   const dm = formData.delivery_method || "";

      //   // Email login
      //   if (formData.login === "email") {
      //     if (!dm) {
      //       newErrors.email = "Email is required";
      //     } else if (!emailRegex.test(dm)) {
      //       newErrors.email = "Invalid email format";
      //     }
      //   }

      //   // Contact login
      //   if (formData.login === "contact") {
      //     if (!dm) {
      //       newErrors.contact = "Contact Number is required";
      //     } else if (!contactRegex.test(dm)) {
      //       newErrors.contact = "Invalid contact format";
      //     }
      //   }
      // }
      if (formData.login === "email") {
        const email = formData.email || formData.general_email;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          newErrors.email = "Please enter a valid email address";
        }
      } else if (formData.login === "contact") {
        const contact = formData.contact || formData.general_contact;
        const contactRegex = /^(018|019|013|017|015|016|014)\d{8}$/;
        if (!contactRegex.test(contact)) {
          newErrors.contact = "Please enter a valid phone number";
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  return { errors, validateForm, setErrors };
};

export default useMemberValidation;
