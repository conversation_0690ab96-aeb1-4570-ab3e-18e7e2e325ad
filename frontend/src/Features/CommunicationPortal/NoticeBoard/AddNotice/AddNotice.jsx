import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import AddNoticeForm from "./AddNoticeForm";
import NoticePreview from "../components/NoticePreview";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import { useNotices } from "../../../../hooks/useNotices";
import { useCurrentUser } from "../hooks/useCurrentUser";

const AddNotice = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser, manualRefresh } = useCurrentUser();
  const [previewData, setPreviewData] = useState({});
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  const {
    creating,
    createSuccess,
    createError,
    message,
    createNotice: createNoticeRedux,
    clearAllSuccess: clearAllSuccessRedux,
    clearErrors: clearErrorsRedux
  } = useNotices();

  // Get the source tab from location state (passed from NoticeBoard)
  const sourceTab = location.state?.sourceTab || null;

  // Handle success
  useEffect(() => {
    if (createSuccess && message) {
      setSuccessMessage("Notice has been successfully created.");
    }
  }, [createSuccess, message]);

  // Handle errors
  useEffect(() => {
    if (createError) {
      setErrorMessage(
        typeof createError === 'string'
          ? createError
          : createError.message || "Failed to create notice. Please try again."
      );
    }
  }, [createError]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearAllSuccessRedux();
      clearErrorsRedux();
    };
  }, [clearAllSuccessRedux, clearErrorsRedux]);

  // Listen for window focus to refresh user data when returning to the page
  useEffect(() => {
    const handleWindowFocus = () => {
      console.log('Window focused, refreshing user data...');
      manualRefresh();
    };

    window.addEventListener('focus', handleWindowFocus);

    return () => {
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, [manualRefresh]);

  const handleBack = () => {
    // Navigate back to the same tab the user came from
    const targetTab = sourceTab || 1; // Default to ongoing tab if no source tab
    navigate("/communication-portal/notice-board", {
      state: { activeTab: targetTab },
      replace: true
    });
  };

  const handlePreview = (formData) => {
    setPreviewData(formData);
  };

  const handleSubmit = async (formData) => {
    try {
      await createNoticeRedux(formData);
    } catch (error) {
      console.error("Error creating notice:", error);
      setErrorMessage("Failed to create notice. Please try again.");
    }
  };

  // Clear success message
  const clearMessage = () => {
    setSuccessMessage("");
  };

  // Handle success message OK button
  const handleSuccessOk = () => {
    // Navigate back to notice board with the correct tab and trigger refresh
    const targetTab = sourceTab || 1; // Default to ongoing tab if no source tab
    navigate("/communication-portal/notice-board", {
      state: {
        activeTab: targetTab,
        noticeId: Date.now() // Trigger refresh by passing a timestamp
      },
      replace: true
    });
  };

  // Clear error message
  const clearErrorMessage = () => {
    setErrorMessage("");
    clearErrorsRedux();
  };

  return (
    <div className="min-h-screen bg-stroke">
      {/* Header */}
      <div className=" shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <button
              onClick={handleBack}
              className="flex items-center text-gray-600  transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              <span className="text-lg font-semibold">
                Create Notice
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-4">
          {/* Left Column - Preview */}
          <div className="order-2 lg:order-1 lg:w-full lg:col-span-4">
            <div className="bg-white rounded-lg shadow-sm p-6 lg:sticky lg:top-8 h-screen lg:h-[calc(100vh-6rem)] overflow-y-auto border-2 ">
              <NoticePreview
                data={previewData}
                currentUser={currentUser}
              />
            </div>
          </div>

          {/* Right Column - Form (wider) */}
          <div className="order-1 lg:order-2 lg:col-span-8 bg-white rounded-lg shadow-sm p-6 ">
            <AddNoticeForm
              onSubmit={handleSubmit}
              onPreview={handlePreview}
              loading={creating}
              currentUser={currentUser}
            />
          </div>
        </div>
      </div>

      {/* Success Message Box */}
      <MessageBox
        message={successMessage}
        clearMessage={clearMessage}
        onOk={handleSuccessOk}
      />

      {/* Error Message */}
      {errorMessage && (
        <MessageBox
          message={errorMessage}
          type="error"
          onClose={clearErrorMessage}
        />
      )}
    </div>
  );
};

export default AddNotice;
