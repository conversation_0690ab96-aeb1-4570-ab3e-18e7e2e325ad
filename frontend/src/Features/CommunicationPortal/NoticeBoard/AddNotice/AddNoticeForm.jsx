import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Upload, X } from "lucide-react";
import PriorityDropdown from "../components/PriorityDropdown";
import LabelSelector from "../components/LabelSelector";
import TimePicker from "../components/TimePicker";
import TowerUnitSelector from "../components/TowerUnitSelector";
import PostAsSelector from "../components/PostAsSelector";
import UserCountDisplay from "../components/UserCountDisplay";
import ErrorMessage from "../../../../Components/MessageBox/ErrorMessage";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";

// Validation schema
const noticeSchema = yup.object().shape({
  priority: yup
    .string()
    .required("Priority is required")
    .oneOf(["low", "normal", "high", "urgent"], "Invalid priority value"),
  label: yup.string().required("Label is required"),
  startDate: yup.string().required("Start date is required"),
  startTime: yup.string().required("Start time is required"),
  endDate: yup.string().required("End date is required"),
  endTime: yup.string().required("End time is required"),
  postAs: yup.string().required("Post as selection is required"),
  postedGroup: yup.number().when('postAs', {
    is: 'group',
    then: (schema) => schema.required("Group selection is required when posting as group"),
    otherwise: (schema) => schema.nullable()
  }),
  postedMember: yup.number().when('postAs', {
    is: 'member',
    then: (schema) => schema.required("Member selection is required when posting as member"),
    otherwise: (schema) => schema.nullable()
  }),
  targetTowers: yup.array().min(1, "At least one tower must be selected"),
  targetUnits: yup.array(),
  attachments: yup.array().min(1, "At least one image is required")
});

/**
 * AddNoticeForm Component
 * Form component for creating notices with layout and state management
 */
const AddNoticeForm = ({
  onSubmit,
  onPreview,
  loading,
  currentUser
}) => {
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [selectedTowers, setSelectedTowers] = useState([]);
  const [selectedUnits, setSelectedUnits] = useState([]);

  // Form setup with validation using useForm (similar to formik but with react-hook-form)
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    getValues,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(noticeSchema),
    mode: "onChange",
    defaultValues: {
      priority: "normal",
      label: "",
      startDate: "",
      startTime: "",
      endDate: "",
      endTime: "",
      postAs: "creator",
      postedGroup: null,
      postedMember: null,
      targetTowers: [],
      targetUnits: [],
      attachments: []
    }
  });

  // Update form values when external state changes
  useEffect(() => {
    setValue('targetTowers', selectedTowers);
  }, [selectedTowers, setValue]);

  useEffect(() => {
    setValue('targetUnits', selectedUnits);
  }, [selectedUnits, setValue]);

  useEffect(() => {
    setValue('attachments', uploadedFiles);
  }, [uploadedFiles, setValue]);

  // Watch all form values for real-time preview
  const watchedValues = watch();

  // Check if form is valid for submission
  const isFormValid = () => {
    const values = getValues();
    return (
      values.priority &&
      values.label &&
      values.startDate &&
      values.startTime &&
      values.endDate &&
      values.endTime &&
      values.postAs &&
      (values.postAs === "creator" ||
        (values.postAs === "group" && values.postedGroup) ||
        (values.postAs === "member" && values.postedMember)) &&
      selectedTowers.length > 0 &&
      uploadedFiles.length > 0
    );
  };

  // Handle file upload
  const handleFileUpload = (files) => {
    setUploadedFiles(files);
  };

  // Handle tower and unit selection
  const handleTowerUnitChange = (towers, units) => {
    setSelectedTowers(towers);
    setSelectedUnits(units);
  };

  // Handle preview
  const handlePreview = () => {
    const previewData = {
      ...watchedValues,
      attachments: uploadedFiles.map(file => ({
        file,
        preview: URL.createObjectURL(file),
        name: file.name
      }))
    };
    onPreview(previewData);
  };

  // Handle form submission
  const handleFormSubmit = (values) => {
    const formData = new FormData();

    // Add basic fields
    formData.append('priority', values.priority);
    if (values.label) formData.append('label', values.label);
    formData.append('start_date', values.startDate);
    formData.append('start_time', values.startTime);
    formData.append('end_date', values.endDate);
    formData.append('end_time', values.endTime);
    formData.append('post_as', values.postAs);

    // Add post as specific fields
    if (values.postAs === 'group' && values.postedGroup) {
      formData.append('posted_group', values.postedGroup);
    }
    if (values.postAs === 'member' && values.postedMember) {
      formData.append('posted_member', values.postedMember);
    }

    // Add target towers and units
    selectedTowers.forEach(towerId => {
      formData.append('target_tower_ids', towerId);
    });
    selectedUnits.forEach(unitId => {
      formData.append('target_unit_ids', unitId);
    });

    // Add attachments
    uploadedFiles.forEach((file) => {
      formData.append('attachments', file);
    });

    onSubmit(formData);
  };

  return (
    <div className="relative">
      {/* Loading Overlay */}
      {loading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50 rounded-lg">
          <LoadingAnimation />
        </div>
      )}

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Notice Author Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-primary mb-4">
            Notice Author
          </h3>

          {/* Creator Name */}
          <div className="mb-4">
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Creator Name
            </label>
            <input
              type="text"
              readOnly
              className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-700 cursor-not-allowed"
              value={
                currentUser?.full_name ||
                currentUser?.fullName ||
                "Current User"
              }
            />
          </div>

          {/* Post As */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Post as <span className="text-primary">*</span>
            </label>
            <Controller
              name="postAs"
              control={control}
              render={({ field }) => (
                <PostAsSelector
                  value={field.value}
                  selectedGroup={watch("postedGroup")}
                  selectedMember={watch("postedMember")}
                  onChange={(postAs, groupId, memberId) => {
                    field.onChange(postAs);
                    setValue("postedGroup", groupId);
                    setValue("postedMember", memberId);
                  }}
                  currentUser={currentUser}
                  error={errors.postAs?.message}
                />
              )}
            />
            {errors.postAs && (
              <ErrorMessage message={errors.postAs.message} />
            )}
          </div>
        </div>

        {/* File Upload Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-primary mb-4">
            Notice Images
          </h3>
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Images <span className="text-primary">*</span>
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={(e) => handleFileUpload(Array.from(e.target.files))}
                className="hidden"
                id="file-upload"
              />
              <label htmlFor="file-upload" className="cursor-pointer">
                <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">
                  Click to upload images or drag and drop
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  PNG, JPG up to 5MB each (max 10 files)
                </p>
              </label>
            </div>

            {/* Display uploaded files */}
            {uploadedFiles.length > 0 && (
              <div className="mt-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {uploadedFiles.map((file, index) => (
                  <div key={index} className="relative">
                    <img
                      src={URL.createObjectURL(file)}
                      alt={file.name}
                      className="w-full h-24 object-cover rounded-lg"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        const newFiles = uploadedFiles.filter((_, i) => i !== index);
                        setUploadedFiles(newFiles);
                      }}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {errors.attachments && (
              <ErrorMessage message={errors.attachments.message} />
            )}
          </div>
        </div>

        {/* Notice Details Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-primary mb-4">
            Notice Details
          </h3>

          {/* Priority and Label */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Priority <span className="text-primary">*</span>
              </label>
              <Controller
                name="priority"
                control={control}
                render={({ field }) => (
                  <PriorityDropdown
                    value={field.value}
                    onChange={field.onChange}
                    error={errors.priority?.message}
                  />
                )}
              />
              {errors.priority && (
                <ErrorMessage message={errors.priority.message} />
              )}
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Label <span className="text-primary">*</span>
              </label>
              <Controller
                name="label"
                control={control}
                render={({ field }) => (
                  <LabelSelector
                    value={field.value}
                    onChange={field.onChange}
                    error={errors.label?.message}
                  />
                )}
              />
              {errors.label && (
                <ErrorMessage message={errors.label.message} />
              )}
            </div>
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Start Date & Time <span className="text-primary">*</span>
              </label>
              <div className="grid grid-cols-2 gap-2">
                <Controller
                  name="startDate"
                  control={control}
                  render={({ field }) => (
                    <input
                      type="date"
                      {...field}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  )}
                />
                <Controller
                  name="startTime"
                  control={control}
                  render={({ field }) => (
                    <TimePicker
                      value={field.value}
                      onChange={field.onChange}
                      error={errors.startTime?.message}
                    />
                  )}
                />
              </div>
              {(errors.startDate || errors.startTime) && (
                <ErrorMessage message={errors.startDate?.message || errors.startTime?.message} />
              )}
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                End Date & Time <span className="text-primary">*</span>
              </label>
              <div className="grid grid-cols-2 gap-2">
                <Controller
                  name="endDate"
                  control={control}
                  render={({ field }) => (
                    <input
                      type="date"
                      {...field}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  )}
                />
                <Controller
                  name="endTime"
                  control={control}
                  render={({ field }) => (
                    <TimePicker
                      value={field.value}
                      onChange={field.onChange}
                      error={errors.endTime?.message}
                    />
                  )}
                />
              </div>
              {(errors.endDate || errors.endTime) && (
                <ErrorMessage message={errors.endDate?.message || errors.endTime?.message} />
              )}
            </div>
          </div>
        </div>

        {/* Target Audience Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-primary mb-4">
            Target Audience
          </h3>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Select Towers & Units <span className="text-primary">*</span>
            </label>
            <TowerUnitSelector
              selectedTowers={selectedTowers}
              selectedUnits={selectedUnits}
              onChange={handleTowerUnitChange}
              error={errors.targetTowers?.message}
            />
            {errors.targetTowers && (
              <ErrorMessage message={errors.targetTowers.message} />
            )}
          </div>

          {/* User Count Display */}
          {selectedUnits.length > 0 && (
            <div className="mt-4">
              <UserCountDisplay unitIds={selectedUnits} />
            </div>
          )}
        </div>

        {/* Form Errors */}
        {formError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <ErrorMessage message={formError} />
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between items-center pt-6 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            <span className="text-primary">*</span> Required fields
          </div>

          <div className="flex gap-3">
            <button
              type="button"
              onClick={handlePreview}
              disabled={!isFormValid() || loading}
              className={`px-6 py-2 border border-primary text-primary rounded-lg transition-colors ${
                isFormValid() && !loading
                  ? "hover:bg-primary hover:text-white"
                  : "opacity-50 cursor-not-allowed"
              }`}
            >
              Preview
            </button>

            <button
              type="submit"
              disabled={!isFormValid() || loading}
              className={`px-6 py-2 rounded-lg transition-colors ${
                isFormValid() && !loading
                  ? "bg-primary text-white hover:bg-primary-dark"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            >
              {loading ? "Creating..." : "Create Notice"}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default AddNoticeForm;
