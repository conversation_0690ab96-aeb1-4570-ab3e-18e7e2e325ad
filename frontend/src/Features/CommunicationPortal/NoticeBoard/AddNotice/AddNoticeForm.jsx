import React from "react";
import { Controller } from "react-hook-form";
import { Upload, X } from "lucide-react";
import PriorityDropdown from "../components/PriorityDropdown";
import LabelSelector from "../components/LabelSelector";
import Calendar from "../components/Calendar";
import TimePicker from "../components/TimePicker";
import TowerUnitSelector from "../components/TowerUnitSelector";
import PostAsSelector from "../components/PostAsSelector";
import UserCountDisplay from "../components/UserCountDisplay";
import ErrorMessage from "../../../../Components/MessageBox/ErrorMessage";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";

/**
 * AddNoticeForm Component
 * Form component for creating notices
 */
const AddNoticeForm = ({
  // Form props
  control,
  handleSubmit,
  watch,
  setValue,
  errors,
  isSubmitting,
  onSubmit,
  onError,

  // State props
  currentUser,
  attachments,

  // Error states
  priorityError,
  labelError,
  startDateError,
  startTimeError,
  endDateError,
  endTimeError,
  postAsError,
  fileUploadError,
  towerError,
  unitError,
  formError,
  apiError,
  dateOrderError,

  // Handlers
  handleFileUpload,
  removeAttachment,
  savePostAsPreference,
  isFormValid
}) => {

  return (
    <div className="relative">
      {/* Loading Overlay */}
      {isSubmitting && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50 rounded-lg">
          <LoadingAnimation />
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit, onError)} className="space-y-6">
        {/* Notice Author Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-primary mb-4">
            Notice Author
          </h3>

          {/* Creator Name and Post as on different rows */}
          <div className="space-y-4">
            {/* Creator Name */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Creator Name
              </label>
              <Controller
                name="creatorName"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="text"
                    readOnly
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-700 cursor-not-allowed"
                    value={
                      currentUser?.full_name ||
                      currentUser?.fullName ||
                      "Current User"
                    }
                  />
                )}
              />
            </div>

            {/* Post as */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Post as <span className="text-primary">*</span>
              </label>
              <Controller
                name="postAs"
                control={control}
                render={({ field }) => (
                  <PostAsSelector
                    value={field.value}
                    selectedGroup={watch("selectedGroupId")}
                    selectedMember={watch("selectedMemberId")}
                    onChange={(postAs, groupId, memberId) => {
                      field.onChange(postAs);
                      setValue("selectedGroupId", groupId);
                      setValue("selectedMemberId", memberId);
                      savePostAsPreference(postAs);
                    }}
                    currentUser={currentUser}
                    error={postAsError}
                  />
                )}
              />
              {errors.postAs && <ErrorMessage message={postAsError} />}
            </div>
          </div>
        </div>

        {/* Notice Information Section */}
        <div className="p-6">
          <h3 className="text-lg font-semibold text-primary mb-4">
            Notice Information
          </h3>

          {/* Attachments */}
          <div className="mb-4">
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Images <span className="text-primary">*</span>
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
                id="file-upload"
              />
              <label
                htmlFor="file-upload"
                className="cursor-pointer flex flex-col items-center"
              >
                <Upload className="w-8 h-8 text-gray-400 mb-2" />
                <span className="text-sm text-gray-600">
                  Click to upload images
                </span>
              </label>
            </div>

            {/* Error Message */}
            <ErrorMessage message={fileUploadError} />

            {/* Display uploaded files */}
            {attachments && attachments.length > 0 && (
              <div className="mt-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {attachments.map((attachment) => (
                  <div key={attachment.id} className="relative">
                    <img
                      src={attachment.url}
                      alt={attachment.name}
                      className="w-full h-24 object-cover rounded-lg"
                    />
                    <button
                      type="button"
                      onClick={() => removeAttachment(attachment.id)}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Priority and Label */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Priority <span className="text-primary">*</span>
              </label>
              <Controller
                name="priority"
                control={control}
                render={({ field }) => (
                  <PriorityDropdown
                    value={field.value}
                    onChange={field.onChange}
                    error={errors.priority?.message}
                  />
                )}
              />
              {errors.priority && <ErrorMessage message={priorityError} />}
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Label <span className="text-primary">*</span>
              </label>
              <Controller
                name="label"
                control={control}
                render={({ field }) => (
                  <LabelSelector
                    value={field.value}
                    onChange={field.onChange}
                    error={errors.label?.message}
                  />
                )}
              />
              {errors.label && <ErrorMessage message={labelError} />}
            </div>
          </div>

        </div>

        {/* Notice Visibility Section */}
        <div className="p-6">
          <h3 className="text-lg font-semibold text-primary mb-4">
            Notice Visibility
          </h3>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Start Date */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Start Date <span className="text-primary">*</span>
              </label>
              <Controller
                name="startDate"
                control={control}
                render={({ field }) => (
                  <Calendar
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select Start Date"
                  />
                )}
              />
              {errors.startDate && <ErrorMessage message={startDateError} />}
            </div>

            {/* Start Time */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Start Time <span className="text-primary">*</span>
              </label>
              <Controller
                name="startTime"
                control={control}
                render={({ field }) => (
                  <TimePicker
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select Start Time"
                  />
                )}
              />
              {errors.startTime && <ErrorMessage message={startTimeError} />}
            </div>

            {/* End Date */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                End Date <span className="text-primary">*</span>
              </label>
              <Controller
                name="endDate"
                control={control}
                render={({ field }) => (
                  <Calendar
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select End Date"
                  />
                )}
              />
              {errors.endDate && <ErrorMessage message={endDateError} />}
            </div>

            {/* End Time */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                End Time <span className="text-primary">*</span>
              </label>
              <Controller
                name="endTime"
                control={control}
                render={({ field }) => (
                  <TimePicker
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select End Time"
                  />
                )}
              />
              {errors.endTime && <ErrorMessage message={endTimeError} />}
            </div>
          </div>

          {/* Date Order Error */}
          {dateOrderError && (
            <div className="mt-4">
              <ErrorMessage message={dateOrderError} />
            </div>
          )}
        </div>

        {/* Target Audience Section */}
        <div className="p-6">
          <h3 className="text-lg font-semibold text-primary mb-4">
            Target Audience
          </h3>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Select Towers & Units <span className="text-primary">*</span>
            </label>
            <Controller
              name="selectedTowers"
              control={control}
              render={({ field: towersField }) => (
                <Controller
                  name="selectedUnits"
                  control={control}
                  render={({ field: unitsField }) => (
                    <TowerUnitSelector
                      selectedTowers={towersField.value || []}
                      selectedUnits={unitsField.value || []}
                      onChange={(towers, units) => {
                        towersField.onChange(towers);
                        unitsField.onChange(units);
                      }}
                      error={towerError || unitError}
                    />
                  )}
                />
              )}
            />
            {(errors.selectedTowers || errors.selectedUnits) && (
              <ErrorMessage message={towerError || unitError} />
            )}
          </div>

          {/* User Count Display */}
          {watch("selectedUnits") && watch("selectedUnits").length > 0 && (
            <div className="mt-4">
              <UserCountDisplay unitIds={watch("selectedUnits")} />
            </div>
          )}
        </div>

        {/* Form Error Message */}
        <ErrorMessage message={formError} />

        {/* API Error Message */}
        <ErrorMessage message={apiError} />

        {/* Submit Button */}
        <div className="flex justify-center">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`w-full px-8 py-3 rounded-md transition duration-200 font-medium ${isFormValid() && !isSubmitting
              ? "bg-primary text-white hover:bg-[#34877A]"
              : "bg-white text-primary border-2 border-primary hover:bg-gray-50"
              } ${isSubmitting ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
              }`}
          >
            {isSubmitting ? "Creating..." : "Send"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddNoticeForm;
