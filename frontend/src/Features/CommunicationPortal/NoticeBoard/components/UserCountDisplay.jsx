import { useState, useEffect } from 'react';
import { FaUsers, FaSpinner } from 'react-icons/fa';
import { useUserCount } from '../hooks/useUserCount';

/**
 * UserCountDisplay Component
 * Shows the total number of users that will receive the notice
 */
const UserCountDisplay = ({ unitIds, targetUnitsData, className = "" }) => {
  const { getUserCount, isLoading } = useUserCount();
  const [userCount, setUserCount] = useState(0);

  useEffect(() => {
    const dataToUse = targetUnitsData || unitIds;
    if (dataToUse && dataToUse.length > 0) {
      const count = getUserCount(dataToUse);
      setUserCount(count);
    } else {
      setUserCount(0);
    }
  }, [unitIds, targetUnitsData, getUserCount]);

  const dataToUse = targetUnitsData || unitIds;
  if (!dataToUse || dataToUse.length === 0) {
    return <span className={className}>0 users</span>;
  }

  // If className is provided, render as simple text (for card footer)
  if (className) {
    return (
      <span className={className}>
        {isLoading ? (
          "Calculating..."
        ) : (
          `${userCount.toLocaleString()} ${userCount === 1 ? 'user' : 'users'}`
        )}
      </span>
    );
  }

  // Default full display (for forms/detailed views)
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div className="flex items-center space-x-2">
        <FaUsers className="w-5 h-5 text-blue-600" />
        <div>
          <h4 className="text-sm font-medium text-blue-900">Target Audience</h4>
          <div className="flex items-center space-x-2 mt-1">
            {isLoading ? (
              <>
                <FaSpinner className="w-4 h-4 text-blue-600 animate-spin" />
                <span className="text-sm text-blue-700">Calculating...</span>
              </>
            ) : (
              <span className="text-lg font-semibold text-blue-700">
                {userCount.toLocaleString()} {userCount === 1 ? 'person' : 'people'}
              </span>
            )}
          </div>
          <p className="text-xs text-blue-600 mt-1">
            This notice will be visible to all residents in the selected units
          </p>
        </div>
      </div>
    </div>
  );
};

export default UserCountDisplay;
