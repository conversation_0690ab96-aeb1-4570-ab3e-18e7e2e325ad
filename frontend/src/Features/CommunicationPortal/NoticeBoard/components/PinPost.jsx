import { useDispatch } from 'react-redux';
import { FaThumbtack } from 'react-icons/fa';
import { togglePinNotice } from '../../../../redux/slices/api/noticeApi';

/**
 * Custom hook for pin/unpin functionality
 */
const usePinPost = () => {
  const dispatch = useDispatch();

  const pinPost = async (noticeId) => {
    try {
      await dispatch(togglePinNotice(noticeId)).unwrap();
      return { success: true };
    } catch (error) {
      console.error('Error pinning notice:', error);
      return { success: false, error };
    }
  };

  const unpinPost = async (noticeId) => {
    try {
      await dispatch(togglePinNotice(noticeId)).unwrap();
      return { success: true };
    } catch (error) {
      console.error('Error unpinning notice:', error);
      return { success: false, error };
    }
  };

  const togglePin = async (noticeId) => {
    try {
      await dispatch(togglePinNotice(noticeId)).unwrap();
      return { success: true };
    } catch (error) {
      console.error('Error toggling pin status:', error);
      return { success: false, error };
    }
  };

  return {
    pinPost,
    unpinPost,
    togglePin
  };
};

/**
 * PinIcon Component
 * Renders the pin icon for pinned notices with click functionality
 */
export const PinIcon = ({ isPinned, onClick, className = "" }) => {
  if (!isPinned) return null;

  return (
    <FaThumbtack
      className={`w-4 h-4 text-red-500 transform rotate-45 cursor-pointer ${className}`}
      onClick={onClick}
    />
  );
};

export default usePinPost;
