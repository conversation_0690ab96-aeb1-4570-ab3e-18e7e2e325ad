import React, { useState } from 'react';
import { Flag, Image as ImageIcon } from "lucide-react";
import { HiUserCircle } from "react-icons/hi";
import { FaUserGroup } from "react-icons/fa6";
import { IoIosNotificationsOutline } from "react-icons/io";
import { useUserCount } from "../hooks/useUserCount";

/**
 * NoticePreview Component
 * Real-time preview of the notice as it will appear when posted
 */
const NoticePreview = ({ data, currentUser, isInModal = false }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [expandedTitle, setExpandedTitle] = useState(false);

  // Use the custom hook for user count calculation
  const targetUnits = data.targetUnits || data.target_units_data?.map((unit) => unit.id) || [];
  const { userCount, loading: loadingUserCount } = useUserCount(targetUnits);

  // Get priority configuration
  const getPriorityConfig = (priority) => {
    const configs = {
      urgent: { color: "#EF4444", bgColor: "#FEF2F2", label: "Urgent" },
      high: { color: "#F59E0B", bgColor: "#FFFBEB", label: "High" },
      normal: { color: "#3D9D9B", bgColor: "#F0FDF4", label: "Normal" },
      low: { color: "#6B7280", bgColor: "#F9FAFB", label: "Low" }
    };
    return configs[priority?.toLowerCase()] || null;
  };

  // Get notice status
  const getStatus = () => {
    if (!data.startDate || !data.startTime || !data.endDate || !data.endTime) {
      return { status: "Draft", color: "#6B7280", bgColor: "#F9FAFB" };
    }

    try {
      const now = new Date();

      // Handle different date formats and ensure proper parsing
      let startDateStr = data.startDate;
      let endDateStr = data.endDate;

      // If dates are in YYYY-MM-DD format, use them directly
      if (typeof data.startDate === "string" && data.startDate.includes("-")) {
        startDateStr = data.startDate;
      } else {
        // Convert to YYYY-MM-DD format if needed
        const startDateObj = new Date(data.startDate);
        startDateStr = startDateObj.toISOString().split("T")[0];
      }

      if (typeof data.endDate === "string" && data.endDate.includes("-")) {
        endDateStr = data.endDate;
      } else {
        // Convert to YYYY-MM-DD format if needed
        const endDateObj = new Date(data.endDate);
        endDateStr = endDateObj.toISOString().split("T")[0];
      }

      // Create datetime objects for comparison
      const startDateTime = new Date(`${startDateStr}T${data.startTime}`);
      const endDateTime = new Date(`${endDateStr}T${data.endTime}`);

      if (now < startDateTime) {
        return { status: "Upcoming", color: "#F59E0B", bgColor: "#FFFBEB" };
      } else if (now >= startDateTime && now <= endDateTime) {
        return { status: "On Going", color: "#10B981", bgColor: "#F0FDF4" };
      } else {
        return { status: "Expired", color: "#EF4444", bgColor: "#FEF2F2" };
      }
    } catch (error) {
      console.warn("Error calculating status in preview:", error);
      return { status: "Draft", color: "#6B7280", bgColor: "#F9FAFB" };
    }
  };

  // Format date and time
  const formatDateTime = (date, time) => {
    if (!date || !time) return "";

    let formattedDate = "";

    // Handle different date formats
    if (typeof date === "string") {
      // If date is in YYYY-MM-DD format (from Calendar component)
      if (date.includes("-") && date.length === 10) {
        const [year, month, day] = date.split("-");
        formattedDate = `${day}-${month}-${year}`;
      } else {
        // If date is already formatted or other string format
        const dateObj = new Date(date);
        const day = dateObj.getDate().toString().padStart(2, "0");
        const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
        const year = dateObj.getFullYear();
        formattedDate = `${day}-${month}-${year}`;
      }
    } else {
      // If date is a Date object
      const dateObj = new Date(date);
      const day = dateObj.getDate().toString().padStart(2, "0");
      const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
      const year = dateObj.getFullYear();
      formattedDate = `${day}-${month}-${year}`;
    }

    // Format time
    let formattedTime = "";
    if (time) {
      // Handle time in HH:MM format
      const [hours, minutes] = time.split(":");
      const hour24 = parseInt(hours, 10);
      const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
      const ampm = hour24 >= 12 ? "pm" : "am";
      formattedTime = `${hour12}:${minutes}${ampm}`;
    }

    return formattedTime ? `${formattedDate} at ${formattedTime}` : formattedDate;
  };

  // Get author information
  const getAuthorInfo = () => {
    switch (data.postAs) {
      case 'creator':
        return {
          name: currentUser?.full_name || currentUser?.fullName || 'Current User',
          icon: <HiUserCircle className="w-5 h-5 text-primary" />,
          type: 'Creator'
        };
      case 'group':
        return {
          name: data.postedGroupName || 'Selected Group',
          icon: <FaUserGroup className="w-5 h-5 text-primary" />,
          type: 'Group'
        };
      case 'member':
        return {
          name: data.postedMemberName || 'Selected Member',
          icon: <HiUserCircle className="w-5 h-5 text-primary" />,
          type: 'Member'
        };
      default:
        return {
          name: 'Unknown',
          icon: <HiUserCircle className="w-5 h-5 text-primary" />,
          type: 'Unknown'
        };
    }
  };

  // Get current status and priority configurations
  const statusConfig = getStatus();
  const priorityConfig = getPriorityConfig(data.priority);
  const authorInfo = getAuthorInfo();

  // Handle image navigation
  const nextImage = () => {
    if (data.attachments && data.attachments.length > 1) {
      setCurrentImageIndex((prev) => (prev + 1) % data.attachments.length);
    }
  };

  const prevImage = () => {
    if (data.attachments && data.attachments.length > 1) {
      setCurrentImageIndex((prev) => (prev - 1 + data.attachments.length) % data.attachments.length);
    }
  };

  // If no data, show empty state
  if (!data || Object.keys(data).length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-8">
        <IoIosNotificationsOutline className="w-16 h-16 text-gray-300 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Notice Preview</h3>
        <p className="text-gray-500 mb-4">
          Fill in the form to see a preview of your notice
        </p>
        <div className="text-sm text-gray-400">
          Your notice will appear here as you type
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4">
        {/* Preview Header */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Preview</h3>
          <p className="text-sm text-gray-600">
            This is how your notice will appear to residents
          </p>
        </div>

        {/* Notice Card */}
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
          {/* Notice Header */}
          <div className="p-4 border-b border-gray-100">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  {authorInfo.icon}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{authorInfo.name}</h4>
                  <p className="text-sm text-gray-500">{authorInfo.type}</p>
                </div>
              </div>

              {/* Status Badge */}
              <div
                className="px-2 py-1 rounded-full text-xs font-medium border"
                style={{
                  backgroundColor: statusConfig.bgColor,
                  color: statusConfig.color,
                  borderColor: statusConfig.color + "40"
                }}
              >
                {statusConfig.status}
              </div>
            </div>

            {/* Priority Badge */}
            {priorityConfig && (
              <div className="flex items-center space-x-2">
                <div
                  className="flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  style={{
                    backgroundColor: priorityConfig.bgColor,
                    color: priorityConfig.color
                  }}
                >
                  <Flag className="w-3 h-3 mr-1" />
                  {priorityConfig.label}
                </div>
              </div>
            )}
          </div>

          {/* Images */}
          {data.attachments && data.attachments.length > 0 && (
            <div className="relative">
              <img
                src={data.attachments[currentImageIndex]?.preview ||
                     (data.attachments[currentImageIndex]?.file ? URL.createObjectURL(data.attachments[currentImageIndex].file) : '')}
                alt={`Notice image ${currentImageIndex + 1}`}
                className="w-full h-48 object-cover"
                onError={(e) => {
                  e.target.style.display = 'none';
                }}
              />

              {/* Image Navigation */}
              {data.attachments.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                  >
                    ←
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                  >
                    →
                  </button>

                  {/* Image Counter */}
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                    {currentImageIndex + 1} / {data.attachments.length}
                  </div>
                </>
              )}
            </div>
          )}

          {/* Notice Details */}
          <div className="p-4">
            {/* Label */}
            {data.label && (
              <div className="mb-3">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {data.label}
                </span>
              </div>
            )}

            {/* Date and Time Information */}
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center">
                <span className="font-medium">Start:</span>
                <span className="ml-2">{formatDateTime(data.startDate, data.startTime)}</span>
              </div>
              <div className="flex items-center">
                <span className="font-medium">End:</span>
                <span className="ml-2">{formatDateTime(data.endDate, data.endTime)}</span>
              </div>
            </div>

            {/* User Count */}
            {targetUnits.length > 0 && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="flex items-center text-sm text-gray-600">
                  <IoIosNotificationsOutline className="w-4 h-4 mr-2" />
                  <span>
                    {loadingUserCount ? "Calculating..." : `${userCount} residents will be notified`}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NoticePreview;
