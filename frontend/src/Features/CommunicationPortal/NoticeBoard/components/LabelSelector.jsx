import { useState, useEffect, useRef } from 'react';
import { FaPlus, FaTag, FaChevronDown, FaTimes } from 'react-icons/fa';
import axiosInstance from '../../../../utils/axiosInstance';

// Emoji validation function
const containsEmoji = (text) => {
  if (!text) return false;
  const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F018}-\u{1F270}]|[\u{238C}-\u{2454}]|[\u{20D0}-\u{20FF}]/u;
  return emojiRegex.test(text);
};

/**
 * LabelSelector Component for Notice Board
 * Handles creating new labels and selecting from existing ones
 */
const LabelSelector = ({ value, onChange, error }) => {
  const [newLabelName, setNewLabelName] = useState('');
  const [existingLabels, setExistingLabels] = useState([]);
  const [isCreatingLabel, setIsCreatingLabel] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showCreateInput, setShowCreateInput] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoadingLabels, setIsLoadingLabels] = useState(false);
  const [emojiError, setEmojiError] = useState('');
  const [labelLimitError, setLabelLimitError] = useState('');
  const [characterLimitError, setCharacterLimitError] = useState('');

  const dropdownRef = useRef(null);
  const inputRef = useRef(null);

  // Load existing labels
  useEffect(() => {
    loadLabels();
  }, []);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
        setShowCreateInput(false);
        setNewLabelName('');
        setSearchTerm('');
        clearErrors();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const loadLabels = async () => {
    setIsLoadingLabels(true);
    try {
      const response = await axiosInstance.get('/api/noticeboard/notices/labels/');
      const labelsData = response.data.results || response.data;
      setExistingLabels(Array.isArray(labelsData) ? labelsData : []);
    } catch (error) {
      console.error('Error loading labels:', error);
      setExistingLabels([]);
    } finally {
      setIsLoadingLabels(false);
    }
  };

  const clearErrors = () => {
    setEmojiError('');
    setLabelLimitError('');
    setCharacterLimitError('');
  };

  const validateLabel = (labelName) => {
    clearErrors();

    if (!labelName.trim()) {
      return false;
    }

    if (labelName.length > 20) {
      setCharacterLimitError('Label name cannot exceed 20 characters');
      return false;
    }

    if (containsEmoji(labelName)) {
      setEmojiError('Label name cannot contain emojis');
      return false;
    }

    return true;
  };

  const handleCreateLabel = async () => {
    if (!validateLabel(newLabelName)) {
      return;
    }

    // Check if label already exists
    const existingLabel = existingLabels.find(
      label => label.name.toLowerCase() === newLabelName.toLowerCase()
    );

    if (existingLabel) {
      onChange(existingLabel.name);
      setIsDropdownOpen(false);
      setShowCreateInput(false);
      setNewLabelName('');
      return;
    }

    setIsCreatingLabel(true);
    try {
      // For now, just use the label name directly
      // In a real implementation, you might want to create the label on the server
      onChange(newLabelName.trim());
      setIsDropdownOpen(false);
      setShowCreateInput(false);
      setNewLabelName('');
      
      // Optionally reload labels to include the new one
      await loadLabels();
    } catch (error) {
      console.error('Error creating label:', error);
    } finally {
      setIsCreatingLabel(false);
    }
  };

  const handleSelectLabel = (labelName) => {
    onChange(labelName);
    setIsDropdownOpen(false);
    setSearchTerm('');
  };

  const handleClearLabel = (e) => {
    e.stopPropagation();
    onChange('');
  };

  const filteredLabels = existingLabels.filter(label =>
    searchTerm === "" ||
    label.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="relative w-full" ref={dropdownRef}>
      {/* Main Input */}
      <div
        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between transition-colors duration-200 ${
          error ? 'border-red-500' : 'border-gray-300'
        } hover:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500`}
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
      >
        <div className="flex items-center space-x-2">
          {value && <FaTag className="w-4 h-4 text-purple-600" />}
          <span className={value ? 'text-gray-900' : 'text-gray-500'}>
            {value || 'Select or create label'}
          </span>
        </div>
        
        <div className="flex items-center space-x-1">
          {value && (
            <button
              type="button"
              onClick={handleClearLabel}
              className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
            >
              <FaTimes className="w-3 h-3 text-gray-400" />
            </button>
          )}
          <FaChevronDown 
            className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
              isDropdownOpen ? 'rotate-180' : ''
            }`} 
          />
        </div>
      </div>

      {/* Dropdown */}
      {isDropdownOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg overflow-hidden">
          {/* Create New Label Button */}
          <div className="p-3 border-b border-gray-200">
            {!showCreateInput ? (
              <button
                type="button"
                onClick={() => {
                  setShowCreateInput(true);
                  setTimeout(() => inputRef.current?.focus(), 100);
                }}
                className="w-full flex items-center space-x-2 text-blue-600 hover:text-blue-700 text-sm"
              >
                <FaPlus className="w-4 h-4" />
                <span>Create new label</span>
              </button>
            ) : (
              <div className="space-y-2">
                <input
                  ref={inputRef}
                  type="text"
                  value={newLabelName}
                  onChange={(e) => setNewLabelName(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleCreateLabel();
                    }
                  }}
                  placeholder="Enter label name..."
                  className="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  maxLength={20}
                />
                
                {/* Error Messages */}
                {(emojiError || labelLimitError || characterLimitError) && (
                  <div className="text-xs text-red-600">
                    {emojiError || labelLimitError || characterLimitError}
                  </div>
                )}
                
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={handleCreateLabel}
                    disabled={isCreatingLabel || !newLabelName.trim()}
                    className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isCreatingLabel ? 'Creating...' : 'Create'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowCreateInput(false);
                      setNewLabelName('');
                      clearErrors();
                    }}
                    className="px-3 py-1 bg-gray-300 text-gray-700 text-xs rounded hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Search Input for Existing Labels */}
          {existingLabels.length > 0 && (
            <div className="p-3 border-b border-gray-200">
              <input
                type="text"
                placeholder="Search labels..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          )}

          {/* Existing Labels */}
          <div className="py-1 max-h-48 overflow-y-auto">
            {isLoadingLabels ? (
              <div className="p-3 text-sm text-gray-500 text-center">
                Loading labels...
              </div>
            ) : existingLabels.length > 0 ? (
              filteredLabels.length > 0 ? (
                filteredLabels.map((label, index) => (
                  <div key={label.id || index}>
                    <button
                      type="button"
                      onClick={() => handleSelectLabel(label.name)}
                      className={`w-full text-left px-3 py-2 text-sm flex items-center space-x-2 hover:bg-gray-50 ${
                        value === label.name ? 'bg-blue-50 text-blue-700' : 'text-gray-900'
                      }`}
                    >
                      <FaTag className="w-4 h-4 text-purple-600" />
                      <span>{label.name}</span>
                    </button>
                    {index < filteredLabels.length - 1 && <div className="h-px bg-gray-200 mx-3" />}
                  </div>
                ))
              ) : (
                <div className="p-3 text-sm text-gray-500 text-center">
                  No labels found matching "{searchTerm}"
                </div>
              )
            ) : (
              <div className="p-3 text-sm text-gray-500 text-center">
                No labels available
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default LabelSelector;
