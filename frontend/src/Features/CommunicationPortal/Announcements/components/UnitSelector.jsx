import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { useDispatch } from 'react-redux';
import { fetchUnitsByTower, fetchTowers } from '../../../../redux/slices/api/announcementApi';

const UnitSelector = ({ value, onChange, selectedTowers = [], placeholder = "Select Units" }) => {
  const dispatch = useDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState('bottom');
  const [units, setUnits] = useState([]);
  const [towers, setTowers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const selectorRef = useRef(null);
  const inputRef = useRef(null);
  const isInitialMount = useRef(true);

  // Fetch towers on component mount
  useEffect(() => {
    const loadTowers = async () => {
      try {
        const result = await dispatch(fetchTowers());
        if (fetchTowers.fulfilled.match(result)) {
          setTowers(result.payload);
        }
      } catch (err) {
        console.error('Error fetching towers:', err);
      }
    };

    loadTowers();
  }, [dispatch]);

  // Debug logging (can be removed in production)
  // console.log('UnitSelector received value:', value);
  // console.log('UnitSelector received selectedTowers:', selectedTowers);

  // Immediately clear units when no towers are selected
  useEffect(() => {
    if (!selectedTowers || selectedTowers.length === 0) {
      setUnits([]);
      // Only clear unit selection if it's not the initial mount
      if (!isInitialMount.current) {
        onChange([]);
      }
      return;
    }
  }, [selectedTowers]);

  // Fetch units from backend based on selected towers
  useEffect(() => {
    const fetchUnits = async () => {
      if (!selectedTowers || selectedTowers.length === 0) {
        return; // Units already cleared by the effect above
      }

      try {
        setLoading(true);
        setError(null);

        console.log('Selected towers:', selectedTowers); // Debug log

        // Filter out 'All' from selectedTowers and get actual tower IDs
        const towerIds = selectedTowers.filter(id => id !== 'All');
        console.log('Filtered tower IDs:', towerIds); // Debug log

        if (selectedTowers.includes('All')) {
          // If 'All' is selected, we should fetch units from all towers
          // Instead of passing null, let's pass all tower IDs to ensure we get units from all towers
          if (towerIds.length > 0) {
            console.log('Fetching units for all towers using tower IDs:', towerIds); // Debug log
            const result = await dispatch(fetchUnitsByTower(towerIds));
            if (fetchUnitsByTower.fulfilled.match(result)) {
              console.log('Fetched all units:', result.payload); // Debug log
              setUnits(result.payload);
            }
          } else {
            // Fallback: fetch all units without tower filter
            console.log('Fetching all units without tower filter'); // Debug log
            const result = await dispatch(fetchUnitsByTower(null));
            if (fetchUnitsByTower.fulfilled.match(result)) {
              console.log('Fetched all units (fallback):', result.payload); // Debug log
              setUnits(result.payload);
            }
          }
        } else if (towerIds.length > 0) {
          // Fetch units for specific towers - pass array of tower IDs
          console.log('Fetching units for specific towers:', towerIds); // Debug log
          const result = await dispatch(fetchUnitsByTower(towerIds));
          if (fetchUnitsByTower.fulfilled.match(result)) {
            console.log('Fetched units for towers:', towerIds, result.payload); // Debug log
            setUnits(result.payload);
          }
        } else {
          // No towers selected, clear units
          console.log('No towers selected, clearing units'); // Debug log
          setUnits([]);
        }
      } catch (err) {
        console.error('Error fetching units:', err);
        setError('Failed to load units');
        setUnits([]);
        // Only clear unit selection if it's not the initial mount
        if (!isInitialMount.current) {
          onChange([]);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchUnits();
  }, [selectedTowers]); // Remove onChange from dependencies

  // Helper function to get tower name by ID
  const getTowerName = (towerId) => {
    const tower = towers.find(t => t.id === towerId);
    return tower ? tower.tower_name : `Tower ${towerId}`;
  };

  // Helper function to get unit name by ID
  const getUnitName = (unitId) => {
    if (unitId === 'All') return 'All Units';
    const unit = units.find(u => u.id === unitId);
    return unit ? unit.unit_name : `Unit ${unitId}`;
  };

  // Group units by tower for hierarchical display
  const getGroupedUnits = () => {
    if (!units.length) return [];

    console.log('Units data for grouping:', units); // Debug log
    console.log('Selected towers:', selectedTowers); // Debug log

    // Group units by tower
    const groupedByTower = {};

    units.forEach(unit => {
      const towerId = unit.tower_id;
      console.log('Unit:', unit.unit_name, 'Tower ID:', towerId, 'Tower Name:', unit.tower_name); // Debug log

      if (towerId !== undefined && towerId !== null) {
        if (!groupedByTower[towerId]) {
          groupedByTower[towerId] = [];
        }
        groupedByTower[towerId].push(unit);
      } else {
        console.warn('Unit without tower_id:', unit); // Debug warning
      }
    });

    console.log('Grouped by tower:', groupedByTower); // Debug log
    console.log('Number of towers found:', Object.keys(groupedByTower).length); // Debug log

    // Convert to array format for rendering
    const result = [];

    // Add "All" option first
    result.push({ type: 'all', id: 'All', name: 'All' });

    // Sort tower IDs to ensure consistent ordering
    const sortedTowerIds = Object.keys(groupedByTower).sort((a, b) => parseInt(a) - parseInt(b));

    console.log('Sorted tower IDs:', sortedTowerIds); // Debug log

    // Add towers and their units
    sortedTowerIds.forEach(towerId => {
      const towerUnits = groupedByTower[towerId];
      if (towerUnits.length > 0) {
        // Use tower_name from unit data if available, otherwise fallback to getTowerName
        const towerName = towerUnits[0].tower_name || getTowerName(parseInt(towerId));

        console.log(`Adding tower: ${towerName} (ID: ${towerId}) with ${towerUnits.length} units`); // Debug log

        // Add tower header
        result.push({
          type: 'tower',
          id: towerId,
          name: towerName,
          units: towerUnits
        });

        // Add units under this tower (sort by unit name)
        const sortedUnits = towerUnits.sort((a, b) => a.unit_name.localeCompare(b.unit_name));
        sortedUnits.forEach(unit => {
          result.push({
            type: 'unit',
            id: unit.id,
            name: unit.unit_name,
            towerId: towerId
          });
        });
      }
    });

    console.log('Final grouped result:', result); // Debug log
    console.log('Total towers found:', sortedTowerIds.length); // Debug log
    return result;
  };

  // Calculate dropdown position
  const calculatePosition = () => {
    if (inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      const dropdownHeight = 300;

      setDropdownPosition(spaceBelow < dropdownHeight && spaceAbove > spaceBelow ? 'top' : 'bottom');
    }
  };

  useEffect(() => {
    if (isOpen) {
      calculatePosition();
      window.addEventListener('scroll', calculatePosition);
      window.addEventListener('resize', calculatePosition);

      return () => {
        window.removeEventListener('scroll', calculatePosition);
        window.removeEventListener('resize', calculatePosition);
      };
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (selectorRef.current && !selectorRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // Immediately filter out invalid units when towers change
  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // If no towers selected, clear all units immediately
    if (!selectedTowers || selectedTowers.length === 0) {
      if (value && value.length > 0) {
        onChange([]);
      }
      return;
    }

    // If we have selected units, filter them immediately based on current towers
    if (value && value.length > 0) {
      const currentValues = value || [];

      // If "All" is selected in units, keep it only if towers are selected
      if (currentValues.includes('All')) {
        // Keep "All" since we have towers selected
        return;
      }

      // For specific unit selections, we need to wait for units to be fetched
      // to properly validate them, but we can do immediate cleanup for obvious cases
      if (units.length > 0 && !loading) {
        const validUnits = currentValues.filter(unit =>
          unit === 'All' || units.some(u => u.id === unit)
        );

        // If some units are no longer valid, update the selection immediately
        if (validUnits.length !== currentValues.length) {
          onChange(validUnits);
        }
      }
    }
  }, [selectedTowers, units, value, loading]);

  const handleUnitChange = (unit, isChecked) => {
    const currentValues = value || [];

    if (unit === 'All') {
      if (isChecked) {
        // When "All" is selected, include both 'All' and all unit IDs
        const allUnitIds = units.map(u => u.id);
        onChange(['All', ...allUnitIds]);
      } else {
        onChange([]);
      }
    } else {
      let newValues;
      if (isChecked) {
        newValues = [...currentValues.filter(v => v !== 'All'), unit];
        // Check if all units are selected
        if (units.every(u => newValues.includes(u.id))) {
          newValues = ['All', ...newValues];
        }
      } else {
        newValues = currentValues.filter(v => v !== unit && v !== 'All');
      }
      onChange(newValues);
    }
  };

  const handleClearAll = () => {
    onChange([]);
  };

  const getDisplayText = () => {
    if (!selectedTowers || selectedTowers.length === 0) {
      return "Select towers first";
    }
    if (!value || value.length === 0) {
      return placeholder;
    }
    if (value.includes('All')) {
      return 'All Units Selected';
    }
    // Count only non-'All' values
    const unitCount = value.filter(unit => unit !== 'All').length;
    return `${unitCount} Unit(s) Selected`;
  };

  const getSelectedDisplay = () => {
    if (!value || value.length === 0) {
      return null;
    }

    // Check if "All" is explicitly selected OR if all available units are selected
    const allUnitsSelected = value.includes('All') || (units.length > 0 && units.every(unit => value.includes(unit.id)));

    if (allUnitsSelected) {
      return (
        <div className="mb-2 p-2 bg-primary bg-opacity-10 rounded-md border border-primary border-opacity-30">
          <div className="text-sm font-medium text-primary mb-1">Selected Units:</div>
          <div className="text-sm text-gray-700">All Unit</div>
        </div>
      );
    }

    return (
      <div className="mb-2 p-2 bg-primary bg-opacity-10 rounded-md border border-primary border-opacity-30">
        <div className="text-sm font-medium text-primary mb-1">Selected Units:</div>
        <div className="flex flex-wrap gap-1">
          {value.filter(unit => unit !== 'All').map((unitId, index) => (
            <span
              key={typeof unitId === 'object' ? `unit-${index}-${unitId.id || index}` : unitId}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary text-white"
            >
              {getUnitName(unitId)}
            </span>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="relative" ref={selectorRef}>
      {/* Selected Values Display */}
      {getSelectedDisplay()}

      {/* Input Field */}
      <div
        ref={inputRef}
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white cursor-pointer flex justify-between items-center focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
      >
        <span className={value && value.length > 0 ? 'text-gray-900 text-sm' : 'text-gray-500 text-xs'}>
          {getDisplayText()}
        </span>
        <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </div>

      {/* Dropdown content */}
      {isOpen && (
        <div className={`absolute z-50 ${
          dropdownPosition === 'top' ? 'bottom-full mb-1' : 'top-full mt-1'
        } w-full bg-white border border-gray-300 rounded-md shadow-lg`}>
          <div className="max-h-60 overflow-y-auto p-3">
            {loading ? (
              <div className="p-2 text-gray-500 text-sm text-center">Loading units...</div>
            ) : error ? (
              <div className="p-2 text-red-500 text-sm text-center">{error}</div>
            ) : !selectedTowers || selectedTowers.length === 0 ? (
              <div className="p-2 text-gray-500 text-sm text-center">
                Please select towers first to see available units
              </div>
            ) : units.length === 0 ? (
              <div className="p-2 text-gray-500 text-sm text-center">No units available</div>
            ) : (
              getGroupedUnits().map((item, index) => {
                if (item.type === 'all') {
                  return (
                    <label key={`all-${item.id}`} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <div className="relative">
                        <input
                          type="checkbox"
                          checked={value?.includes('All') || false}
                          onChange={(e) => handleUnitChange('All', e.target.checked)}
                          className="sr-only"
                        />
                        <div className={`w-5 h-5 border-2 rounded ${
                          value?.includes('All')
                            ? 'bg-primary border-primary'
                            : 'border-gray-300 bg-white'
                        } flex items-center justify-center`}>
                          {value?.includes('All') && (
                          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                        </div>
                      </div>
                      <span className="text-sm text-gray-700 font-medium">{item.name}</span>
                    </label>
                  );
                } else if (item.type === 'tower') {
                  return (
                    <div key={`tower-${item.id}-${index}`} className="text-sm text-gray-600 font-medium px-2 py-1 bg-gray-50 border-b border-gray-200">
                      {item.name}
                    </div>
                  );
                } else if (item.type === 'unit') {
                  return (
                    <label key={`unit-${item.id}-${index}`} className="flex items-center space-x-3 p-2 pl-6 hover:bg-gray-50 rounded cursor-pointer">
                      <div className="relative">
                        <input
                          type="checkbox"
                          checked={value?.includes(item.id) || false}
                          onChange={(e) => handleUnitChange(item.id, e.target.checked)}
                          className="sr-only"
                        />
                        <div className={`w-5 h-5 border-2 rounded ${
                          value?.includes(item.id)
                            ? 'bg-primary border-primary'
                            : 'border-gray-300 bg-white'
                        } flex items-center justify-center`}>
                          {value?.includes(item.id) && (
                          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                        </div>
                      </div>
                      <span className="text-sm text-gray-700">{item.name}</span>
                    </label>
                  );
                }
                return null;
              })
            )}
          </div>
          <div className="border-t border-gray-200 p-3">
            <div className="flex gap-2">
              <button
                type="button"
                onClick={handleClearAll}
                className="flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors text-sm font-medium"
                title="Clear All"
              >
                Clear
              </button>
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="flex-1 bg-primary text-white py-2 px-4 rounded-md hover:bg-[#34877A] transition-colors text-sm font-medium"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default React.memo(UnitSelector);
