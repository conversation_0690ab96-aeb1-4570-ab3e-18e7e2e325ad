import React from 'react';
import {
    FaEdit,
    FaHistory,
    FaFlag,
    FaBell,
    FaThumbtack,
    FaComments,
    FaTrash,
    FaUndo
} from 'react-icons/fa';

/**
 * AnnouncementActionMenu Component
 * Displays different action menus based on announcement status
 */
const AnnouncementActionMenu = ({
    announcement,
    onEdit,
    onHistory,
    onMoveToExpired,
    onReminder,
    onPinPost,
    onDirectCommunication,
    onDelete,
    onRestore,
    onClose
}) => {
    const { status } = announcement;

    // Define actions based on status
    const getActionsForStatus = () => {
        switch (status) {
            case 'ongoing':
                return [
                    {
                        icon: FaBell,
                        label: 'Reminder',
                        action: onReminder,
                        className: 'text-black-600 hover:text-black-800'
                    },
                    {
                        icon: FaEdit,
                        label: 'Edit',
                        action: onEdit,
                        className: 'text-black-700 hover:text-black-900'
                    },
                    {
                        icon: FaHistory,
                        label: 'History',
                        action: onHistory,
                        className: 'text-black-700 hover:text-black-900'
                    },
                    {
                        icon: FaTrash,
                        label: 'Move Expired',
                        action: onMoveToExpired,
                        className: 'text-black-600 hover:text-black-800'
                    },
                    {
                        icon: FaThumbtack,
                        label: announcement.pinned ? 'Unpin Post' : 'Pin Post',
                        action: onPinPost,
                        className: announcement.pinned ? 'text-black-600 hover:text-black-800' : 'text-black-600 hover:text-black-800'
                    },
                    {
                        icon: FaComments,
                        label: 'Direct Communication',
                        action: onDirectCommunication,
                        className: 'text-black-600 hover:text-black-800  '
                    }
                ];

            case 'upcoming':
                return [
                    {
                        icon: FaEdit,
                        label: 'Edit',
                        action: onEdit,
                        className: 'text-black-700 hover:text-black-900'
                    },
                    {
                        icon: FaHistory,
                        label: 'History',
                        action: onHistory,
                        className: 'text-black-700 hover:text-black-900'
                    },
                    {
                        icon: FaTrash,
                        label: 'Move Expired',
                        action: onMoveToExpired,
                        className: 'text-black-600 hover:text-black-800'
                    }
                ];

            case 'expired':
                const actions = [
                    {
                        icon: FaEdit,
                        label: 'Edit',
                        action: onEdit,
                        className: 'text-black-700 hover:text-black-900'
                    },
                    {
                        icon: FaHistory,
                        label: 'History',
                        action: onHistory,
                        className: 'text-black-700 hover:text-black-900'
                    }
                ];

                // Add restore option if manually expired and still within valid time range
                if (announcement.manuallyExpired) {
                    const now = new Date();
                    const endDateTime = new Date(`${announcement.endDate} ${announcement.endTime}`);
                    if (now <= endDateTime) {
                        actions.push({
                            icon: FaUndo,
                            label: 'Restore',
                            action: onRestore,
                            className: 'text-blue-600 hover:text-blue-800'
                        });
                    }
                }

                // Always add delete option for expired announcements
                actions.push({
                    icon: FaTrash,
                    label: 'Delete',
                    action: onDelete,
                    className: 'text-red-600 hover:text-red-800'
                });

                return actions;

            default:
                return [];
        }
    };

    const actions = getActionsForStatus();

    return (
        <div className="absolute right-0 top-6 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 min-w-[200px]">
            {actions.map((action, index) => {
                const IconComponent = action.icon;
                return (
                    <button
                        key={index}
                        className={`flex items-center w-full px-3 py-2 text-sm hover:bg-gray-100 transition-colors whitespace-nowrap ${action.className}`}
                        onClick={() => {
                            action.action(announcement.id);
                            onClose();
                        }}
                    >
                        <IconComponent className="w-3 h-3 mr-2 flex-shrink-0" />
                        <span className="truncate">{action.label}</span>
                    </button>
                );
            })}
        </div>
    );
};

export default AnnouncementActionMenu;
