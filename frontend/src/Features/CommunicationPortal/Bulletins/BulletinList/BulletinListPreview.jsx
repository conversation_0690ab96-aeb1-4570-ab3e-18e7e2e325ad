import React, { useState, useEffect, useRef } from "react";
import { FaUserGroup } from "react-icons/fa6";
import { HiDotsHorizontal } from "react-icons/hi";
import { HiUserCircle } from "react-icons/hi";
import BulletinActionMenu from "../components/BulletinActionMenu";
import { PinIcon } from "../components/PinPost";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";
import UserCountDisplay from "../../Announcements/components/UserCountDisplay";

/**
 * BulletinListPreview Component
 * Renders the list of bulletins with their preview cards in a row-wise masonry layout
 */
const BulletinListPreview = ({
  // Data props
  bulletins,
  loading,

  // UI state props
  openDropdownId,
  dropdownRef,
  currentTab,

  // Handlers
  handleDropdownToggle,
  handleEditBulletin,
  handleBulletinHistory,
  handleMoveToArchive,
  handleReminder,
  handlePinPost,
  handlePinIconClick,
  handleDirectCommunication,
  handleDeleteBulletin,
  handleRestoreBulletin,
  handleImageClick,
  handleDocumentClick,

  // Helper functions
  isDocument,
  getFileIcon
}) => {
  const containerRef = useRef(null);
  const [positions, setPositions] = useState([]);
  const [containerHeight, setContainerHeight] = useState(0);
  const [isLayoutReady, setIsLayoutReady] = useState(false);

  // Format date and time
  const formatDateTime = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    const period = hours >= 12 ? "pm" : "am";
    return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
  };

  // Two-pass layout: first render naturally, then measure and position
  useEffect(() => {
    if (!containerRef.current || bulletins.length === 0) {
      setPositions([]);
      setContainerHeight(0);
      setIsLayoutReady(false);
      return;
    }

    const calculateLayout = () => {
      // Add null check to prevent errors
      if (!containerRef.current) {
        return;
      }

      const containerWidth = containerRef.current.offsetWidth;
      const cardWidth = 350; // Fixed card width
      const gap = 8; // 8px gap between cards

      // Calculate number of columns that fit
      const columns = Math.max(
        1,
        Math.floor((containerWidth + gap) / (cardWidth + gap))
      );
      const columnHeights = new Array(columns).fill(0);
      const newPositions = [];

      // Get all card elements to measure their actual heights
      const cardElements =
        containerRef.current.querySelectorAll("[data-card-id]");

      if (cardElements.length === bulletins.length) {
        // All cards are rendered, measure their heights
        bulletins.forEach((_, index) => {
          const cardElement = cardElements[index];
          const cardHeight = cardElement ? cardElement.offsetHeight : 300;

          // Find the shortest column for optimal space usage
          const shortestColumn = columnHeights.indexOf(
            Math.min(...columnHeights)
          );

          // Position the card
          newPositions.push({
            left: shortestColumn * (cardWidth + gap),
            top: columnHeights[shortestColumn]
          });

          // Update column height with actual card height + gap
          columnHeights[shortestColumn] += cardHeight + gap;
        });

        setPositions(newPositions);
        setContainerHeight(Math.max(...columnHeights) - gap);
        setIsLayoutReady(true);
      } else {
        // Cards not fully rendered yet, wait for next frame
        setIsLayoutReady(false);
        // Add null check before requesting animation frame
        if (containerRef.current) {
          requestAnimationFrame(calculateLayout);
        }
      }
    };

    // Start layout calculation
    setIsLayoutReady(false);
    const timeoutId = setTimeout(calculateLayout, 50);

    // Recalculate when content changes or window resizes
    const resizeObserver = new ResizeObserver(() => {
      // Add null check to prevent errors during resize
      if (containerRef.current) {
        calculateLayout();
      }
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      clearTimeout(timeoutId);
      resizeObserver.disconnect();
    };
  }, [bulletins]);

  if (loading) {
    return (
      <div className="col-span-full flex justify-center items-center py-12">
        <LoadingAnimation />
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="relative w-full"
      style={{
        height: isLayoutReady ? `${containerHeight}px` : "auto"
      }}
    >
      {bulletins.map((bulletin, index) => {
        const position = positions[index];

        return (
          <div
            key={bulletin.id}
            data-card-id={bulletin.id}
            className={`bg-white border border-primary rounded-lg p-4 shadow-sm ${
              isLayoutReady ? "absolute" : "relative mb-2"
            }`}
            style={{
              width: "350px",
              ...(isLayoutReady && position
                ? {
                    left: `${position.left}px`,
                    top: `${position.top}px`
                  }
                : {})
            }}
          >
            {/* Header */}
            <div className="flex justify-between items-start mb-3">
              <div className="flex items-center space-x-2">
                <div className="w-[24px] h-[24px] rounded-full flex items-center justify-center">
                  {bulletin.postAs === "creator" ? (
                    <HiUserCircle className="w-8 h-8" color="gray" />
                  ) : bulletin.postAs === "group" ? (
                    <FaUserGroup className="w-8 h-8" color="gray" />
                  ) : bulletin.postAs === "member" ? (
                    <FaUserGroup className="w-8 h-8" color="gray" />
                  ) : (
                    <FaUserGroup className="w-8 h-8" color="gray" />
                  )}
                </div>
                <div>
                  <h3 className="text-black text-[14px] font-bold">
                    {bulletin.author}
                  </h3>
                  <p className="text-black text-[11px] font-bold">
                    Creator{" "}
                    {bulletin.creatorName ||
                      bulletin.creator_name ||
                      bulletin.author}
                  </p>
                  <p className="text-primary text-[11px] font-bold">
                    {formatDateTime(bulletin.created_at || bulletin.createdAt)}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {/* Pin Icon */}
                <PinIcon
                  announcement={bulletin}
                  onPinIconClick={handlePinIconClick}
                  currentTab={currentTab}
                />
                {/* User Count Display - notification icon and user count */}
                <UserCountDisplay announcement={bulletin} />
                <div
                  className="relative"
                  ref={openDropdownId === bulletin.id ? dropdownRef : null}
                >
                  <HiDotsHorizontal
                    className="w-[16px] h-[16px] text-primary cursor-pointer hover:text-primaryDark"
                    onClick={() => handleDropdownToggle(bulletin.id)}
                  />
                  {openDropdownId === bulletin.id && (
                    <BulletinActionMenu
                      bulletin={bulletin}
                      onEdit={handleEditBulletin}
                      onHistory={handleBulletinHistory}
                      onMoveToArchive={handleMoveToArchive}
                      onReminder={handleReminder}
                      onPinPost={handlePinPost}
                      onDirectCommunication={handleDirectCommunication}
                      onDelete={handleDeleteBulletin}
                      onRestore={handleRestoreBulletin}
                      onClose={() => handleDropdownToggle(null)}
                    />
                  )}
                </div>
              </div>
            </div>

            {/* Label - Next line */}
            {bulletin.label && (
              <div className="text-[12px] mb-2">
                <div className="flex flex-wrap gap-1">
                  {bulletin.label.split(",").map((label, index) => (
                    <span
                      key={index}
                      className="bg-labelBg text-black text-[10px] px-2 py-1 rounded font-bold"
                    >
                      {label.trim()}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Title */}
            <h4 className="text-black text-[14px] font-semibold mb-2 line-clamp-2">
              {bulletin.title}
            </h4>

            {/* Description */}
            <p className="text-textMedium text-[12px] mb-3">
              {bulletin.description}
            </p>

            {/* Attachments - Show only if they exist */}
            {bulletin.attachments && bulletin.attachments.length > 0 && (
              <div className="mb-3">
                <div className="space-y-2">
                  {(() => {
                    console.log(
                      `[BulletinListPreview] Bulletin ${bulletin.id} attachments:`,
                      bulletin.attachments
                    );
                    console.log(
                      `[BulletinListPreview] Attachment structure for bulletin ${bulletin.id}:`,
                      bulletin.attachments?.map((att) => ({
                        id: att.id,
                        file_url: att.file_url,
                        file_name: att.file_name,
                        url: att.url,
                        name: att.name,
                        type: att.type,
                        file_type: att.file_type
                      }))
                    );

                    const totalAttachments = bulletin.attachments.length;

                    return (
                      <div className="space-y-2">
                        {/* Main/First Image/PDF - Display prominently */}
                        <div
                          key={bulletin.attachments[0].id || 0}
                          className="relative bg-gray-100 overflow-hidden cursor-pointer hover:opacity-90 transition-all duration-200 border border-gray-200 w-[316px] h-[243px] rounded-[16px]"
                          onClick={() =>
                            isDocument(
                              bulletin.attachments[0].file_name ||
                                bulletin.attachments[0].name
                            )
                              ? handleDocumentClick(bulletin.attachments[0])
                              : handleImageClick(
                                  bulletin.attachments[0],
                                  bulletin
                                )
                          }
                        >
                          {isDocument(
                            bulletin.attachments[0].file_name ||
                              bulletin.attachments[0].name
                          ) ? (
                            <div className="w-full h-full flex items-center justify-center">
                              <div className="flex items-center">
                                {getFileIcon(
                                  bulletin.attachments[0].file_name ||
                                    bulletin.attachments[0].name
                                )}
                                <div className="ml-2 text-left">
                                  <div className="text-sm font-medium text-gray-900">
                                    {(
                                      bulletin.attachments[0].file_name ||
                                      bulletin.attachments[0].name
                                    )
                                      ?.toLowerCase()
                                      .endsWith(".pdf")
                                      ? "PDF Document"
                                      : "Word Document"}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {bulletin.attachments[0].file_name ||
                                      bulletin.attachments[0].name}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <img
                              src={
                                bulletin.attachments[0].file_url ||
                                bulletin.attachments[0].url ||
                                bulletin.attachments[0]
                              }
                              alt={
                                bulletin.attachments[0].file_name ||
                                bulletin.attachments[0].name ||
                                `Attachment 1`
                              }
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                console.error(
                                  "Image load error for:",
                                  bulletin.attachments[0].file_url
                                );
                                e.target.style.display = "none";
                              }}
                            />
                          )}
                        </div>

                        {/* Additional Images/Files - Show as thumbnails if more than 1 */}
                        {totalAttachments > 1 && (
                          <div className="flex gap-2 overflow-x-auto">
                            {bulletin.attachments
                              .slice(1)
                              .map((attachment, index) => (
                                <div
                                  key={attachment.id || index + 1}
                                  className="relative flex-shrink-0 bg-gray-100 overflow-hidden cursor-pointer hover:opacity-90 transition-all duration-200 border border-gray-200"
                                  style={{
                                    width: "75px",
                                    height: "57.1px",
                                    borderRadius: "16px"
                                  }}
                                  onClick={() =>
                                    isDocument(
                                      attachment.file_name || attachment.name
                                    )
                                      ? handleDocumentClick(attachment)
                                      : handleImageClick(attachment, bulletin)
                                  }
                                >
                                  {isDocument(
                                    attachment.file_name || attachment.name
                                  ) ? (
                                    <div className="w-full h-full flex items-center justify-center">
                                      <div className="scale-75">
                                        {getFileIcon(
                                          attachment.file_name ||
                                            attachment.name
                                        )}
                                      </div>
                                    </div>
                                  ) : (
                                    <img
                                      src={
                                        attachment.file_url ||
                                        attachment.url ||
                                        attachment
                                      }
                                      alt={
                                        attachment.file_name ||
                                        attachment.name ||
                                        `Attachment ${index + 2}`
                                      }
                                      className="w-full h-full object-cover"
                                      onError={(e) => {
                                        console.error(
                                          "Image load error for:",
                                          attachment.file_url
                                        );
                                        e.target.style.display = "none";
                                      }}
                                    />
                                  )}
                                </div>
                              ))}
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default BulletinListPreview;
