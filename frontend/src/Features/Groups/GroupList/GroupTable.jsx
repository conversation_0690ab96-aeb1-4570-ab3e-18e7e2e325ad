import React, { useMemo } from "react";
import NoData from "../../../Components/Table/NoData";
import ViewButton from "../../../Components/Buttons/ViewButton";
import UserThumbnailGroup from "../../../Components/Thumbnails/UserThumbnailGroup";
import { useNavigate } from "react-router-dom";
import Status from "../../../Components/Text/Status";
import TableCellText from "../../../Components/Table/TableCellText";
import TableCell from "../../../Components/Table/TableCell";

const GroupTable = ({ groupList }) => {
  const navigate = useNavigate();

  // ✅ Memoize sorted data to avoid unnecessary sorting on re-renders
  const sortedGroups = useMemo(() => {
    return [...groupList].sort((a, b) => b.id - a.id); // or sort by created_at
  }, [groupList]);

  return (
    <div className="relative overflow-x-auto max-h-[70vh] overflow-y-auto bg-white">
      <table className="w-full text-sm text-left rtl:text-right">
        <thead className="bg-subprimary border-b border-subprimary sticky top-0">
          <tr>
            <th className="px-3 font-[700] py-2 text-base">Members</th>
            <th className="px-3 font-[700] py-2 text-base">Group Name</th>
            <th className="px-3 font-[700] py-2 text-base">Group Description</th>
            <th className="px-3 font-[700] py-2 text-base">Roles</th>
            <th className="px-2 font-[700] py-2 text-base">Status</th>
            <th className="w-[5%] px-3 font-[700] py-2 text-base text-center">Actions</th>
          </tr>
        </thead>
        <tbody>
          {sortedGroups.length === 0 ? (
            <NoData message="There are no groups" />
          ) : (
            sortedGroups.map((group) => (
              <tr
                key={group.id}
                className="bg-white border-b hover:bg-gray-50 cursor-pointer min-h-[100px]"
                onClick={() => navigate(`/groupProfile/${group.id}`)}
              >
                <TableCell>
                  {group?.members?.length === 0 ? (
                    <p className="py-1 text-sm text-gray-500">No members</p>
                  ) : (
                    <UserThumbnailGroup
                      userPhotos={group.members.map((member) => member.photo_low_quality)}
                    />
                  )}
                </TableCell>
                <TableCellText data={group?.group_name} />
                <TableCellText data={group?.group_description} />
                <TableCellText data={group?.roles.map((role) => role.role_name)} />
                <TableCell>
                  {group.is_active ? (
                    <Status color="primary" text="Active" />
                  ) : (
                    <Status color="error" text="Inactive" />
                  )}
                </TableCell>
                <TableCell>
                  <ViewButton url={`/groupProfile/${group.id}`} />
                </TableCell>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
};

export default GroupTable;
