import React, { useCallback, useEffect, useState } from "react";
import Heading from "../../../Components/HeadingComponent/Heading";
import Button from "../../../Components/FormComponent/ButtonComponent/Button";
import { FaPrint, FaDownload } from "react-icons/fa";
import { GoPlus } from "react-icons/go";
import { FaPlus } from "react-icons/fa6";

import { useNavigate } from "react-router-dom";
import FilterSelect1 from "../../../Components/FilterSelect1/FilterSelect1";
import SearchBar from "../../../Components/Search/SearchBar";
import TableSkeleton from "../../../Components/Loaders/TableSkeleton";
import useGroupList from "../useGroupList";
import { checkPermission } from "../../../utils/permissionUtils";
import { exportToExcel, printTable } from "../../../utils/exportPrintExcel";
import logo from "./../../../assets/user/user.png";
import { RiFilter3Fill } from "react-icons/ri";
import GroupTable from "./GroupTable";
import FilterButton from "../../../Components/FormComponent/ButtonComponent/FilterButton";

const GroupList = () => {
  const navigate = useNavigate();
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);
  const [showFilters, setShowFilters] = useState(false);

  // Hook to get group list data
  const { groupList, loading } = useGroupList();

  const statusOptions = [
    { label: "Active", value: "1" },
    { label: "Inactive", value: "0" }
  ];

  useEffect(() => {
    const fetchPermission = async () => {
      const permissionGranted = await checkPermission("org", 9);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  const handleExport = useCallback(() => {
    const exportData = groupList.map((group) => ({
      Name: group.group_name,
      Description: group.group_description,
      Role: group.roles?.map((role) => role.role_name).join(", "),

      Status: Number(group.is_active) === 1 ? "Active" : "Inactive"
    }));

    exportToExcel(exportData, "EstateLink Group List","Group List");
  }, [groupList]);

  const handlePrint = useCallback(() => {
    const title = "Group List";
    const logoUrl = logo;
    const fileName = `EstateLink Group List ${
      new Date().toISOString().split("T")[0]
    }.pdf`;
    const columns = [
      { header: "Name", accessor: (item) => item.group_name },
      { header: "Description", accessor: (item) => item.group_description },
      {
        header: "Role",
        accessor: (item) => item.roles.map((role) => role.role_name)
      },

      {
        header: "Status",
        accessor: (item) =>
          Number(item.is_active) === 1 ? "Active" : "Inactive"
      }
    ];

    printTable(groupList, columns, title, logoUrl, fileName);
  }, [groupList]);

const toggleFilters = () => {
  setShowFilters((prev) => !prev);
  setFilterActive((prev) => !prev); // this was missing!
};

  const [filterActive, setFilterActive] = useState(false);

  // Early returns must come before any other hooks
  if (loadingPermission) {
    return (
      <div className="flex items-center justify-center my-12">
        <TableSkeleton />
      </div>
    );
  }

  if (!hasPermission) {
    navigate("/not-authorized");
    return null; // Important to return null after navigate
  }

  return (
    <div className="sticky top-0 z-10 bg-white ">
      <div className="my-6 pb-4 flex items-center justify-between">
        <Heading title="Group List" size="2xl" color="black" />
        <div className="flex items-center space-x-4">
          <Button
            icon={FaDownload}
            variant="download"
            size="large"
            onClick={handleExport}
            disabled={!groupList.length}
            iconSize="medium"
          />
          <Button
            variant="download"
            size="large"
            icon={FaPrint}
            onClick={handlePrint}
            disabled={!groupList.length}
            iconSize="medium"
          />
          {/* <Button
            variant="download"
            size="xl"
            iconSize="xl"
            icon={RiFilter3Fill}
            onClick={toggleFilters}
          >
            Filter
          </Button> */}
          <FilterButton active={filterActive} onClick={toggleFilters}>
            Filter
          </FilterButton>
          <Button
            icon={FaPlus}
            onClick={() => navigate(`/add-group`)}
            className="bg-primary hover:bg-primary-dark text-white"
          >
            Add Group
          </Button>
        </div>
      </div>

      {showFilters && (
        <div className="flex justify-end items-center space-x-4 my-4">
          <FilterSelect1
            placeholder="Status"
            options={statusOptions}
            paramKey="status"
            onApply={(filters) => console.log("Applied Filters:", filters)}
          />
          <SearchBar />
        </div>
      )}

      <div className="my-4">
        {loading ? (
          <div className="flex items-center justify-center my-12">
            <TableSkeleton />
          </div>
        ) : groupList.length === 0 ? (
          <div className="text-center my-12 text-gray-500">No groups found</div>
        ) : (
          <GroupTable groupList={groupList} />
        )}
      </div>
    </div>
  );
};

export default GroupList;
