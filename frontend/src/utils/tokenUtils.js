

//  Created By <PERSON><PERSON><PERSON>
export const getAccessToken = () => {
    return localStorage.getItem("access_token");
  };
  
  export const getRefreshToken = () => {
    return localStorage.getItem("refresh_token");
  };
  
  export const setTokens = (accessToken, refreshToken) => {
    localStorage.setItem("access_token", accessToken);
    localStorage.setItem("refresh_token", refreshToken);
  };
  
  export const clearTokens = () => {
    localStorage.removeItem("access_token");
    localStorage.removeItem("refresh_token");
    localStorage.removeItem("member");
  };
  
  export const hasAccessToken = () => {
    return !!getAccessToken();
  };
  