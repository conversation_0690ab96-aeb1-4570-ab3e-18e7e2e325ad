import { utils, write, writeFile } from "xlsx";
import { saveAs } from "file-saver";

export const exportToExcel = (data, fileName = "export", customHeader = "Member List") => {
  // 1 Create worksheet from data, starting from A3 (3rd row)
  const worksheet = utils.json_to_sheet(data, { origin: "A3" });

  // 2 Add custom header at A1
  utils.sheet_add_aoa(worksheet, [[customHeader]], { origin: "A1" });

  // 3 Merge A1 to last column of data for header
  const colCount = Object.keys(data[0] || {}).length;
  worksheet["!merges"] = [{
    s: { r: 0, c: 0 }, // A1
    e: { r: 0, c: colCount - 1 } // e.g., C1 if 3 columns
  }];

  const workbook = utils.book_new();
  utils.book_append_sheet(workbook, worksheet, "Sheet1");

  const excelBuffer = write(workbook, {
    bookType: "xlsx",
    type: "array"
  });

  const blob = new Blob([excelBuffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  });

  saveAs(blob, `${fileName} ${new Date().toISOString().split("T")[0]}.xlsx`);
};
export const printTable = (members, columns, title, logoUrl, fileName = "document.pdf") => {
  const printStyles = `
    @media print {
      body * {
        visibility: hidden;
      }
      .print-container,
      .print-container * {
        visibility: visible;
      }
      .print-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        padding: 20px;
        background: white;
      }
      .print-header {
        text-align: center;
        margin-bottom: 20px;
      }
      .print-header h2 {
        margin: 0;
        font-size: 20px;
      }
      .print-header img {
        height: 50px;
        width: auto;
        margin-bottom: 10px;
      }
      .print-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
        font-size: 12px;
      }
      .print-table th, 
      .print-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }
      .print-table th {
        background-color: #f2f2f2;
        font-weight: bold;
      }
      @page {
        size: auto;
        margin: 10mm;
      }
    }
  `;

  // Create print container
  const printContainer = document.createElement('div');
   printContainer.className = 'print-container';
      // ${logoUrl ? `<img src="${logoUrl}" alt="Logo">` : ''}

  // Create header
  const headerHTML = `
    <div class="print-header">
      <h2>${title}</h2>
      <p>Printed on: ${new Date().toLocaleDateString()}</p>
    </div>
  `;

  // Create simple table structure
  let tableHTML = `
    <table class="print-table">
      <thead>
        <tr>
          ${columns.map(col => `<th>${col.header}</th>`).join('')}
        </tr>
      </thead>
      <tbody>
        ${members.map(item => `
          <tr>
            ${columns.map(col => {
              const value = col.accessor(item) ?? '';
              return `<td>${value}</td>`;
            }).join('')}
          </tr>
        `).join('')}
      </tbody>
    </table>
  `;

  printContainer.innerHTML = headerHTML + tableHTML;

  // Add print styles
  const styleElement = document.createElement('style');
  styleElement.innerHTML = printStyles;

  // Append to DOM
  document.body.appendChild(printContainer);
  document.head.appendChild(styleElement);

  // Print and cleanup
  const printAndCleanup = () => {
    window.print();
    
    setTimeout(() => {
      if (document.body.contains(printContainer)) {
        document.body.removeChild(printContainer);
        document.head.removeChild(styleElement);
      }
    }, 500);
  };

  // For better browser compatibility
  if (window.matchMedia) {
    const mediaQueryList = window.matchMedia('print');
    const listener = (mql) => {
      if (!mql.matches) {
        document.body.removeChild(printContainer);
        document.head.removeChild(styleElement);
        mediaQueryList.removeListener(listener);
      }
    };
    mediaQueryList.addListener(listener);
  }

  // Trigger print
  setTimeout(printAndCleanup, 100);
};