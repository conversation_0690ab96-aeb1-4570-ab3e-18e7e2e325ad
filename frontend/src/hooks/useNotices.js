import { useDispatch, useSelector } from 'react-redux';
import {
  fetchNotices,
  fetchNoticeById,
  createNotice,
  updateNotice,
  deleteNotice,
  togglePinNotice,
  moveToExpired,
  incrementViews,
  restoreNotice,
  updateNoticeStatuses
} from '../redux/slices/api/noticeApi';
import {
  clearErrors,
  clearSuccess,
  setActiveTab,
  clearSelectedNotice,
  resetCreateState,
  resetUpdateState,
  clearAllStates
} from '../redux/slices/notices/noticeSlice';

/**
 * Custom hook for managing notices
 * Provides a clean interface to the notices Redux state and actions
 */
export const useNotices = () => {
  const dispatch = useDispatch();
  
  // Select state from Redux store
  const {
    notices,
    selectedNotice,
    loading,
    creating,
    updating,
    deleting,
    error,
    createError,
    updateError,
    deleteError,
    message,
    createSuccess,
    updateSuccess,
    deleteSuccess,
    towers,
    units,
    towersLoading,
    unitsLoading,
    attachments,
    uploadingAttachment,
    attachmentError,
    activeTab
  } = useSelector((state) => state.notices);

  // Action creators
  const actions = {
    // Load notices
    loadNotices: (params = {}) => dispatch(fetchNotices(params)),
    
    // Load single notice
    loadNotice: (id) => dispatch(fetchNoticeById(id)),
    
    // Create notice
    createNotice: (data) => dispatch(createNotice(data)),
    
    // Update notice
    updateNotice: ({ id, data }) => dispatch(updateNotice({ id, data })),
    
    // Delete notice
    removeNotice: (id) => dispatch(deleteNotice(id)),
    
    // Toggle pin status
    toggleNoticePin: (id) => dispatch(togglePinNotice(id)),
    
    // Move to expired
    moveNoticeToExpired: (id) => dispatch(moveToExpired(id)),
    
    // Restore expired notice
    restoreExpiredNotice: (id) => dispatch(restoreNotice(id)),
    
    // Increment views
    incrementNoticeViews: (id) => dispatch(incrementViews(id)),
    
    // Update all statuses
    updateAllStatuses: () => dispatch(updateNoticeStatuses()),
    
    // UI actions
    setActiveTab: (tab) => dispatch(setActiveTab(tab)),
    clearSelectedNotice: () => dispatch(clearSelectedNotice()),
    
    // Error and success management
    clearErrors: () => dispatch(clearErrors()),
    clearSuccess: () => dispatch(clearSuccess()),
    clearAllSuccess: () => dispatch(clearSuccess()),
    resetCreateState: () => dispatch(resetCreateState()),
    resetUpdateState: () => dispatch(resetUpdateState()),
    clearAllStates: () => dispatch(clearAllStates())
  };

  // Computed values
  const computed = {
    // Check if any operation is in progress
    isLoading: loading || creating || updating || deleting,
    
    // Check if there are any errors
    hasErrors: !!(error || createError || updateError || deleteError || attachmentError),
    
    // Check if there are any success states
    hasSuccess: !!(createSuccess || updateSuccess || deleteSuccess),
    
    // Get notices by status
    ongoingNotices: notices?.filter(notice => notice.status === 'ongoing') || [],
    upcomingNotices: notices?.filter(notice => notice.status === 'upcoming') || [],
    expiredNotices: notices?.filter(notice => notice.status === 'expired') || [],
    
    // Get pinned notices
    pinnedNotices: notices?.filter(notice => notice.isPinned) || [],
    
    // Get notices count by status
    statusCounts: {
      ongoing: notices?.filter(notice => notice.status === 'ongoing').length || 0,
      upcoming: notices?.filter(notice => notice.status === 'upcoming').length || 0,
      expired: notices?.filter(notice => notice.status === 'expired').length || 0,
      total: notices?.length || 0
    }
  };

  // Helper functions
  const helpers = {
    // Get notice by ID
    getNoticeById: (id) => notices?.find(notice => notice.id === parseInt(id)),
    
    // Check if notice is pinned
    isNoticePinned: (id) => {
      const notice = notices?.find(notice => notice.id === parseInt(id));
      return notice?.isPinned || false;
    },
    
    // Get notices by priority
    getNoticesByPriority: (priority) => 
      notices?.filter(notice => notice.priority === priority) || [],
    
    // Get notices by label
    getNoticesByLabel: (label) => 
      notices?.filter(notice => notice.label === label) || [],
    
    // Search notices
    searchNotices: (searchTerm) => {
      if (!searchTerm) return notices || [];
      const term = searchTerm.toLowerCase();
      return notices?.filter(notice => 
        (notice.internalTitle && notice.internalTitle.toLowerCase().includes(term)) ||
        (notice.author && notice.author.toLowerCase().includes(term)) ||
        (notice.label && notice.label.toLowerCase().includes(term))
      ) || [];
    },
    
    // Filter notices by date range
    filterNoticesByDateRange: (startDate, endDate) => {
      if (!startDate || !endDate) return notices || [];
      return notices?.filter(notice => {
        const noticeStart = new Date(notice.startDate);
        const noticeEnd = new Date(notice.endDate);
        const filterStart = new Date(startDate);
        const filterEnd = new Date(endDate);
        
        return (noticeStart >= filterStart && noticeStart <= filterEnd) ||
               (noticeEnd >= filterStart && noticeEnd <= filterEnd) ||
               (noticeStart <= filterStart && noticeEnd >= filterEnd);
      }) || [];
    }
  };

  return {
    // State
    notices,
    selectedNotice,
    loading,
    creating,
    updating,
    deleting,
    error,
    createError,
    updateError,
    deleteError,
    message,
    createSuccess,
    updateSuccess,
    deleteSuccess,
    towers,
    units,
    towersLoading,
    unitsLoading,
    attachments,
    uploadingAttachment,
    attachmentError,
    activeTab,
    
    // Actions
    ...actions,
    
    // Computed values
    ...computed,
    
    // Helper functions
    ...helpers
  };
};

export default useNotices;
