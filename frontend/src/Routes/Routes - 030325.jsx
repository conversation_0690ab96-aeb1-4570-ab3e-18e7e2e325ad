import { createBrowserRouter } from "react-router-dom";
import Login from "../Authentication/Login/Login";
import Dashboard from "../Layout/Dashboard";
import MemberList from "../Features/Members/MemberList/MemberList";

// import MemberProfile from "../Features/Members/MemberProfile/MemberProfile";
import ForgotPassword from "../Authentication/ForgotPassword/ForgotPassword";
import VerifyCode from "../Authentication/VerifyCode/VerifyCode";
import SetNewPassword from "../Authentication/SetNewPassword/SetNewPassword";
import MessageBox from "../Components/MessageBox/MessageBox";
import AddGroup from "../Features/Groups/AddGroup/AddGroup";
import GroupProfile from "../Features/Groups/GroupProfile/GroupProfile";
import RoleList from "../Features/Roles/RoleList/RoleList";
import AddRole from "../Features/Roles/AddRole/AddRole";
import RoleProfile from "../Features/Roles/RoleProfile/RoleProfile";
import AddTower from "../Features/TowersAndUnits/Towers/AddTower/AddTower";
import TowerList from "../Features/TowersAndUnits/Towers/TowerList/TowerList";
import TowerProfile from "../Features/TowersAndUnits/Towers/TowerProfile/TowerProfile";
import AddUnit from "../Features/TowersAndUnits/Units/AddUnits/AddUnit";
import Logindummy from "../Authentication/Login/Logindummy";
import About from "../Authentication/Login/About";
import AnnouncementList from "../Features/CommunicationPortal/Announcements/AnnouncementList";
import AddOwner from "../Features/TowersAndUnits/Owner/AddOwner/AddOwner";
import OwnerProfile from "../Features/TowersAndUnits/Owner/OwnerProfile/OwnerProfile";
import AddResident from "../Features/TowersAndUnits/Resident/AddResident/AddResident";
import AddUnitStaff from "../Features/TowersAndUnits/UnitStaff/AddUnitStaff/AddUnitStaff";
// import MemberEdit from "../Features/Members/MemberEdit/MemberEdit";
import AddMemberPage from "../pages/AddMemberPage";
import DemoPage from "../pages/DemoPage";
import MemberListPage from "../pages/MemberListPage";
import GroupsPage from "../pages/Groups/GroupsPage";
import GroupProfilePage from "../pages/Groups/GroupProfilePage";
import ProtectedRoute from "./ProtectedRoute";
import MemberProfilePage from "../pages/Members/MemberProfilePage";
// import MemberRoleAndTypeEdit from "../Features/Members/MemberEdit/RoleAndMemberTypeEdit/MemberRoleAndTypeEdit";
import LoginCredentialEdit from "../Features/Login/LoginCredential/LoginCredentialEdit";
import GeneralInformationEditPage from "../pages/Members/GeneralInformationEditPage";
// import GeneralInformationEdit from "../Features/Members/MemberEdit/GeneralInformationEdit";
// import LoginCredentialEdit from "../Features/Login/LoginCredential/LoginCredentialEdit";
// import MemberRoleAndTypeEdit from "../Features/Members/MemberEdit/RoleAndMemberTypeEdit/MemberRoleAndTypeEdit";

export const router = createBrowserRouter([
  { path: "/login", element: <Login /> },
  { path: "logindummy", element: <Login /> },
  { path: "/forgotPassword", element: <ForgotPassword /> },
  { path: "/verifyCode", element: <VerifyCode /> },
  { path: "/setNewPassword", element: <SetNewPassword /> },
  { path: "messageBox", element: <MessageBox /> },
  { path: "", element: <ProtectedRoute />, children: [
    { path: "dashboard", element: <Dashboard />, children: [
      { path: "member-list", element: <MemberListPage /> },
      { path: "create-member", element: <AddMemberPage /> },
      { path: "member-profile/:id", element: <MemberProfilePage /> },
      { path: "general-information-edit/:id", element: <GeneralInformationEditPage /> },
      { path: "demoPage", element: <DemoPage /> },
      
      // Group
      {path: "addGroup/:id?", element: <AddGroup /> },
      { path: "groups", element: <GroupsPage /> },
      { path: "groupProfile/:id", element: <GroupProfilePage /> },
      
      // Role
      { path: "addRole", element: <AddRole /> },
      { path: "roleList", element: <RoleList /> },
      { path: "roleProfile", element: <RoleProfile /> },

      // Tower
      { path: "towerList", element: <TowerList /> },
      { path: "addTower", element: <AddTower /> },
      { path: "towerProfile", element: <TowerProfile /> },

      // Units
      { path: "addUnit", element: <AddUnit /> },
      {
        path: "about",
        element: <About />
      },

      // Add Owner
      {
        path: "addOwner",
        element: <AddOwner />
      },
      {
        path: "ownerProfile",
        element: <OwnerProfile />
      },

      // AddUnitStaff
      {
        path: "addUnitStaff",
        element: <AddUnitStaff />
      },

      // AddResident
      {
        path: "addResident",
        element: <AddResident />
      },

      // Announcements
      {
        path: "announcementsList",
        element: <AnnouncementList />,
      },
      {
        path: "login-credential-edit/:id",
        element: <LoginCredentialEdit />,
      },
        ],
      },
    ],
  },
]);