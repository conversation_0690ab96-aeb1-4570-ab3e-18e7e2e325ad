// src/routes/route.js
import { createBrowserRouter } from "react-router-dom"; 
import Login from "../Authentication/Login/Login";
import Dashboard from "../Layout/Dashboard";
import ForgotPassword from "../Authentication/ForgotPassword/ForgotPassword";
import VerifyCode from "../Authentication/VerifyCode/VerifyCode";
import SetNewPassword from "../Authentication/SetNewPassword/SetNewPassword";
import MessageBox from "../Components/MessageBox/MessageBox";
import Logindummy from "../Authentication/Login/Logindummy";
import About from "../Authentication/Login/About";

import AddGroup from "../Features/Groups/AddGroup/AddGroup";
import RoleList from "../Features/Roles/RoleList/RoleList";
import AddRole from "../Features/Roles/AddRole/AddRole";
import RoleProfile from "../Features/Roles/RoleProfile/RoleProfile";

// Towers
import AddTower from "../Features/TowersAndUnits/Towers/pages/AddTower";
import ViewTowers from "../Features/TowersAndUnits/Towers/pages/ViewTowers";

//Units
import AddUnit from "../Features/TowersAndUnits/Units/AddUnits/AddUnit";
import AddOwner from "../Features/TowersAndUnits/Owner/AddOwner/AddOwner";
import OwnerProfile from "../Features/TowersAndUnits/Owner/OwnerProfile/OwnerProfile";
import AddResident from "../Features/TowersAndUnits/Resident/AddResident/AddResident";
import AddUnitStaff from "../Features/TowersAndUnits/UnitStaff/AddUnitStaff/AddUnitStaff";

//Units
import AddMemberPage from "../pages/AddMemberPage";
import DemoPage from "../pages/DemoPage";
import MemberListPage from "../pages/MemberListPage";
import GroupsPage from "../pages/Groups/GroupsPage";
import GroupProfilePage from "../pages/Groups/GroupProfilePage";
import MemberProfilePage from "../pages/Members/MemberProfilePage";
import LoginCredentialEdit from "../Features/Login/LoginCredential/LoginCredentialEdit";
import GeneralInformationEditPage from "../pages/Members/GeneralInformationEditPage";
import NotAuthorized from "../Components/NotAuthorized/NotAuthorized";
import ProtectedRoute from "./ProtectedRoute";
import EditTower from "../Features/TowersAndUnits/Towers/pages/EditTower";
import MemberTypeAndRoleEditPage from "../pages/Members/MemberTypeAndRoleEditPage";
import UnitDetails from "../Features/TowersAndUnits/UnitDetails/pages/UnitDetails";

export const router = createBrowserRouter([
  {
    path: "/",
    element: <Dashboard />,
    children: [
      // {
      //   path: "/",
      //   element: <MemberListPage />,
      // },
      {
        path: "/",
        element: (
          <ProtectedRoute requiredPermission={3}>
            <MemberListPage />
          </ProtectedRoute>
        ),
      },
      {
        path: "member-list",
        element: (
          <ProtectedRoute requiredPermission={3}>
            <MemberListPage />
          </ProtectedRoute>
        ),
      },
      
      {
        path: "create-member",
        element: (
          <ProtectedRoute requiredPermission={1}>
            <AddMemberPage />
          </ProtectedRoute>
        ),
      },
      {
        path: "member-profile/:id",
        element: (
          <ProtectedRoute requiredPermission={3}>
            <MemberProfilePage />
          </ProtectedRoute>
        ),
      },
      {
        path: "general-information-edit/:id",
        element: (
          <ProtectedRoute requiredPermission={2}>
            <GeneralInformationEditPage />
          </ProtectedRoute>
        ),
      },
      {
        path: "demoPage",
        element: <DemoPage />,
      },

      // Group Routes (Protected using permission 7 or 9)
      {
        path: "addGroup/:id?", 
        element: (
          <ProtectedRoute requiredPermission={7}>
            <AddGroup />
          </ProtectedRoute>
        ),
      },
      {
        path: "groupProfile/:id",
        element: (
          <ProtectedRoute requiredPermission={9}>
            <GroupProfilePage />
          </ProtectedRoute>
        ),
      },
      {
        path: "groups",
        element: (
          <ProtectedRoute requiredPermission={9}>
            <GroupsPage />
          </ProtectedRoute>
        ),
      },

      // Role Routes (Protected using permission 6)
      {
         path: "addRole/:id?",
        element: (
          <ProtectedRoute requiredPermission={4}>
            <AddRole />
          </ProtectedRoute>
        ),
      },
      
      {
        path: "roleList",
        element: (
          <ProtectedRoute requiredPermission={6}>
            <RoleList />
          </ProtectedRoute>
        ),
      },
      {
        path: "roleProfile/:id",
        element: (
          <ProtectedRoute requiredPermission={6}>
            <RoleProfile />
          </ProtectedRoute>
        ),
      },

      // Tower Routes
      {
        path: "ViewTowers",
        element: (
          // <ProtectedRoute requiredPermission={12}>
            <ViewTowers />
          // </ProtectedRoute>
        ),
      },
      {
        path: "addTower",
        element: (
          // <ProtectedRoute requiredPermission={10}>
            <AddTower />
          // </ProtectedRoute>
        ),
      },
      {
        path: "editTower/:id",
        element: (
          // <ProtectedRoute requiredPermission={11}>
            <EditTower />
          // </ProtectedRoute>
        ),
      },

      // Units Routes
      {
        path: "add-unit/:id",
        element: <AddUnit />,
      },
      {
        path: "about",
        element: <About />,
      },

      // Owner Routes
      {
        path: "add-owner",
        element: <AddOwner />,
      },
      {
        path: 'unit-details/:id',
        element: <UnitDetails/>,
      },
      {
        path: "ownerProfile",
        element: <OwnerProfile />,
      },

      // Unit Staff Routes
      {
        path: "addUnitStaff",
        element: <AddUnitStaff />,
      },

      {
        path: "addResident",
        element: <AddResident />,
      },

      // Credential Edit Route
      {
        path: "login-credential-edit/:id",
        element: <LoginCredentialEdit />,
      },
      {
        path: "MemberTypeAndRoleEdit/:id",
        element: <MemberTypeAndRoleEditPage />,
      },
    ],
  },
  // Public/Unprotected routes:
  {
    path: "not-authorized",
    element: <NotAuthorized />,
  },
  {
    path: "logindummy",
    element: <Logindummy />,
  },
  {
    path: "/login",
    element: <Login />,
  },
  {
    path: "forgotPassword",
    element: <ForgotPassword />,
  },
  {
    path: "verifyCode",
    element: <VerifyCode />,
  },
  {
    path: "setNewPassword",
    element: <SetNewPassword />,
  },
  {
    path: "messageBox",
    element: <MessageBox />,
  },
  
]);
