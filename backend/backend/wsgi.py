"""
WSGI config for backend project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/howto/deployment/wsgi/
"""
## Old wsgi

# import os

# from django.core.wsgi import get_wsgi_application

# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

# application = get_wsgi_application()


import os
import django
from django.core.wsgi import get_wsgi_application
import sys

# Ensure the project is in the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set Django settings module environment variable
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

# Call the script to populate permissions before starting the application
from group_role.auto_populate_permissions import AutoPopulatePermissions

# Call the script to populate permissions before starting the application
def run_populate_permissions():
    auto_populate = AutoPopulatePermissions()  # Instantiate the class
    auto_populate.populate_permissions()  # Call the function

run_populate_permissions()  # Execute the function

# Now start the Django application
django.setup()
application = get_wsgi_application()