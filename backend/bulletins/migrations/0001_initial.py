# Generated by Django 5.2.3 on 2025-07-13 13:47

import bulletins.models
import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('group_role', '0003_membersrole_is_group_membersrole_is_member'),
        ('towers', '0005_unit_status_color_alter_unit_unit_status'),
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Bulletin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('post_as', models.CharField(choices=[('creator', 'Creator'), ('group', 'Group'), ('member', 'Member')], default='creator', max_length=20)),
                ('group_name', models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ('member_name', models.CharField(blank=True, max_length=255, null=True)),
                ('priority', models.CharField(choices=[('urgent', 'Urgent'), ('high', 'High'), ('normal', 'Normal'), ('low', 'Low')], max_length=10)),
                ('label', models.CharField(blank=True, default='', max_length=500, null=True)),
                ('status', models.CharField(choices=[('current', 'Current'), ('pending', 'Pending'), ('archive', 'Archive')], default='pending', max_length=10)),
                ('views', models.IntegerField(default=0)),
                ('is_pinned', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='bulletin_creator', to='user.member')),
                ('creator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_bulletins', to='user.member')),
                ('posted_group', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='group_bulletins', to='group_role.group')),
                ('posted_member', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='member_bulletins', to='user.member')),
                ('target_towers', models.ManyToManyField(blank=True, related_name='bulletins', to='towers.tower')),
                ('target_units', models.ManyToManyField(blank=True, related_name='bulletins', to='towers.unit')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='bulletin_updater', to='user.member')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BulletinAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to=bulletins.models.upload_to_bulletin_attachments, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp', 'pdf', 'doc', 'docx'])])),
                ('file_name', models.CharField(max_length=255)),
                ('file_type', models.CharField(max_length=50)),
                ('file_size', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('bulletin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='bulletins.bulletin')),
            ],
        ),
        migrations.CreateModel(
            name='BulletinHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('edited_at', models.DateTimeField(auto_now_add=True)),
                ('changes', models.JSONField()),
                ('comment', models.TextField(blank=True, null=True)),
                ('action', models.CharField(choices=[('created', 'Created'), ('updated', 'Updated'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='updated', max_length=20)),
                ('bulletin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='bulletins.bulletin')),
                ('edited_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.member')),
            ],
        ),
    ]
