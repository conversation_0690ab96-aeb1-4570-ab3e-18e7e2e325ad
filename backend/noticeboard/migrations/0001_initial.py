# Generated by Django 5.2.3 on 2025-07-27 12:43

import django.core.validators
import django.db.models.deletion
import noticeboard.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('group_role', '0003_membersrole_is_group_membersrole_is_member'),
        ('towers', '0005_unit_status_color_alter_unit_unit_status'),
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Notice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('internal_title', models.CharField(blank=True, max_length=255)),
                ('post_as', models.CharField(choices=[('creator', 'Creator'), ('group', 'Group'), ('member', 'Member')], default='creator', max_length=20)),
                ('group_name', models.CharField(blank=True, max_length=255, null=True)),
                ('member_name', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('priority', models.CharField(choices=[('urgent', 'Urgent'), ('high', 'High'), ('normal', 'Normal'), ('low', 'Low')], max_length=10)),
                ('label', models.CharField(blank=True, default='', max_length=500, null=True)),
                ('start_date', models.DateField()),
                ('start_time', models.TimeField()),
                ('end_date', models.DateField()),
                ('end_time', models.TimeField()),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('upcoming', 'Upcoming'), ('ongoing', 'Ongoing'), ('expired', 'Expired')], default='draft', max_length=10)),
                ('views', models.IntegerField(default=0)),
                ('is_pinned', models.BooleanField(default=False)),
                ('manually_expired', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='notice_creator', to='user.member')),
                ('creator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_notices', to='user.member')),
                ('posted_group', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='group_notices', to='group_role.group')),
                ('posted_member', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='member_notices', to='user.member')),
                ('target_towers', models.ManyToManyField(blank=True, related_name='notices', to='towers.tower')),
                ('target_units', models.ManyToManyField(blank=True, related_name='notices', to='towers.unit')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='notice_updater', to='user.member')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='NoticeAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to=noticeboard.models.upload_to_notice_attachments, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp', 'heic', 'heif', 'svg', 'eps', 'ai', 'cr2', 'cr3', 'nef', 'arw', 'dng', 'ico', 'apng', 'psd', 'exr', 'dds'])])),
                ('file_name', models.CharField(max_length=255)),
                ('file_type', models.CharField(max_length=50)),
                ('file_size', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('notice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='noticeboard.notice')),
            ],
        ),
        migrations.CreateModel(
            name='NoticeHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('edited_at', models.DateTimeField(auto_now_add=True)),
                ('changes', models.JSONField()),
                ('edited_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.member')),
                ('notice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='noticeboard.notice')),
            ],
        ),
    ]
