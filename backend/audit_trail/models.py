# models.py
from django.db import models
from user.models import Member  # adjust the import path

class AuditTrail(models.Model):
    EVENT_CHOICES = [
        ('MEMBER_CREATED','MEMBER CREATED'),
        ('MEMBER_UPDATED','MEMBER UPDATED'),
        ('MEMBER_STATUS_CHANGED','MEMBER STATUS CHANGED'),

        ('GROUP_CREATE', 'Group Created'),
        ('GROUP_UPDATE', 'Group Updated'),
        ('GROUP_STATUS_CHANGE', 'GROUP STATUS CHANGE'),
        
        ('ROLE_CREATED','ROLE CREATED'),
        ('ROLE_UPDATED','ROLE UPDATED'),
        ('ROLE_STATUS_CHANGED','ROLE STATUS CHANGED')
    ]

    id = models.BigAutoField(primary_key=True)
    event_type = models.CharField(max_length=21, choices=EVENT_CHOICES)
    table_name = models.CharField(max_length=50)
    row_id = models.BigIntegerField()
    member = models.ForeignKey(Member, on_delete=models.SET_NULL, null=True, blank=True)
    old_data = models.JSONField(null=True, blank=True)
    new_data = models.JSONField(null=True, blank=True)
    description = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.event_type} - {self.table_name} {self.row_id}"

