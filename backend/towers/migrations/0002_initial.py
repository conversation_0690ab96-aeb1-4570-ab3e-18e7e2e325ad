# Generated by Django 5.0.4 on 2025-05-25 14:54

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('towers', '0001_initial'),
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='floor',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='created_floors', to='user.member'),
        ),
        migrations.AddField(
            model_name='floor',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='updated_floors', to='user.member'),
        ),
        migrations.AddField(
            model_name='owner',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='created_owners', to='user.member'),
        ),
        migrations.AddField(
            model_name='owner',
            name='member',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.member'),
        ),
        migrations.AddField(
            model_name='owner',
            name='ownership_transfer_from',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transferred_ownerships', to='user.member'),
        ),
        migrations.AddField(
            model_name='owner',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='updated_owners', to='user.member'),
        ),
        migrations.AddField(
            model_name='ownerdocs',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='created_owner_docs', to='user.member'),
        ),
        migrations.AddField(
            model_name='ownerdocs',
            name='owner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='towers.owner'),
        ),
        migrations.AddField(
            model_name='ownerdocs',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='updated_owner_docs', to='user.member'),
        ),
        migrations.AddField(
            model_name='resident',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='created_residents', to='user.member'),
        ),
        migrations.AddField(
            model_name='resident',
            name='member',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.member'),
        ),
        migrations.AddField(
            model_name='resident',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='updated_residents', to='user.member'),
        ),
        migrations.AddField(
            model_name='residentdocs',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='created_resident_docs', to='user.member'),
        ),
        migrations.AddField(
            model_name='residentdocs',
            name='resident',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='docs', to='towers.resident'),
        ),
        migrations.AddField(
            model_name='residentdocs',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='updated_resident_docs', to='user.member'),
        ),
        migrations.AddField(
            model_name='tower',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='created_towers', to='user.member'),
        ),
        migrations.AddField(
            model_name='tower',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='updated_towers', to='user.member'),
        ),
        migrations.AddField(
            model_name='floor',
            name='tower',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='towers.tower'),
        ),
        migrations.AddField(
            model_name='unit',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='created_units', to='user.member'),
        ),
        migrations.AddField(
            model_name='unit',
            name='floor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='towers.floor'),
        ),
        migrations.AddField(
            model_name='unit',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='updated_units', to='user.member'),
        ),
        migrations.AddField(
            model_name='resident',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='towers.unit'),
        ),
        migrations.AddField(
            model_name='ownerdocs',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='towers.unit'),
        ),
        migrations.AddField(
            model_name='owner',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='towers.unit'),
        ),
        migrations.AddField(
            model_name='unitdocs',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='created_unit_docs', to='user.member'),
        ),
        migrations.AddField(
            model_name='unitdocs',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='docs', to='towers.unit'),
        ),
        migrations.AddField(
            model_name='unitdocs',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='updated_unit_docs', to='user.member'),
        ),
        migrations.AddField(
            model_name='unitstaff',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='created_unitstaff', to='user.member'),
        ),
        migrations.AddField(
            model_name='unitstaff',
            name='member',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.member'),
        ),
        migrations.AddField(
            model_name='unitstaff',
            name='unit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='towers.unit'),
        ),
        migrations.AddField(
            model_name='unitstaff',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='updated_unitstaff', to='user.member'),
        ),
    ]
