from django.db import models
from django.core.validators import FileExtensionValidator
from user.models import Member
import os

class Tower(models.Model):
    tower_name = models.CharField(max_length=255)
    tower_number = models.IntegerField(null=True)
    description = models.TextField(blank=True, null=True)
    photo = models.ImageField(upload_to='towers/',null=True,validators=[FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'heic'])])
    num_floors = models.IntegerField(default=0)
    num_units = models.IntegerField(default=0)
    unit_naming_type = models.CharField(max_length=255)
    add_tower_number_to_unit_name = models.BooleanField(default=0)
    units_per_floor = models.CharField(max_length=255,default="Same as Every Floor")
    created_by = models.ForeignKey(Member, null=True,on_delete=models.DO_NOTHING, related_name='created_towers')
    created_at = models.DateTimeField(auto_now_add=True,null=True)
    updated_by = models.ForeignKey(Member, null=True,on_delete=models.DO_NOTHING, related_name='updated_towers')
    updated_at = models.DateTimeField(auto_now = True,null=True) 


    def __str__(self):
        return self.tower_name
    
    def save(self, *args, **kwargs):
        # Ensure that the directory exists before saving
        if self.photo:
            # Get the directory where the image will be stored
            media_directory = os.path.join('media', 'towers')

            # Create the directory if it doesn't exist
            if not os.path.exists(media_directory):
                os.makedirs(media_directory)

        super(Tower, self).save(*args, **kwargs)

class Floor(models.Model):
    tower = models.ForeignKey(Tower,on_delete=models.CASCADE)
    floor_no = models.IntegerField()
    number_of_units = models.IntegerField()
    created_by = models.ForeignKey(Member, null=True,on_delete=models.DO_NOTHING, related_name='created_floors')
    created_at = models.DateTimeField(auto_now_add=True,null=True)
    updated_by = models.ForeignKey(Member, null=True,on_delete=models.DO_NOTHING, related_name='updated_floors')
    updated_at = models.DateTimeField(auto_now = True,null=True)

    def __str__(self):
        return f"Floor {self.floor_no} in Tower {self.tower.tower_name}"
        return f"Unit {self.unit_name} on Floor {self.floor.floor_no}"

def upload_to_unit_docs(instance, filename):
    """
    Returns the upload path for a file based on the associated Unit's details.
    The path is constructed using the Unit's id and unit_name.
    For example, if the Unit has an id of 5 and a unit_name "UnitA",
    the file will be stored in the directory: 'unit_docs/5_UnitA/filename.pdf'.
    """
    return os.path.join('unit_docs', filename)

class Unit(models.Model):
    """
    Unit model stores details about a unit in a building.
    """
    UNIT_STATUS_CHOICES = [
        ('no_owner', 'No Owner'),
        ('available', 'Available'),
        ('occupied', 'Occupied'),
        ('unknown', 'Unknown'),
    ]

    # Color mapping matching frontend
    STATUS_COLORS = {
        'no_owner': '#FFFFFF',    # White (bg-white)
        'available': '#FF8682',   # Light red (bg-[#FF8682])
        'occupied': '#3D9D9B',    # Teal (bg-[#3D9D9B])
        'unknown': '#E5E7EB',     # Gray (bg-[#E5E7EB])
    }
    floor = models.ForeignKey(Floor, on_delete=models.CASCADE)
    unit_name = models.CharField(max_length=20)
    # unit_status = models.CharField(max_length=255)
    unit_status = models.CharField(
        max_length=20,
        choices=UNIT_STATUS_CHOICES,
        default='no_owner'
    )
    status_color = models.CharField(
        max_length=7,
        default='#FFFFFF',
        help_text="Hex color code for unit status"
    )
    created_by = models.ForeignKey(Member, null=True, on_delete=models.DO_NOTHING, related_name='created_units')
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_by = models.ForeignKey(Member, null=True, on_delete=models.DO_NOTHING, related_name='updated_units')
    updated_at = models.DateTimeField(auto_now=True, null=True)

    # Additional unit details
    area = models.IntegerField(null=True)
    number_of_rooms = models.IntegerField(null=True)
    number_of_bathrooms = models.IntegerField(null=True)
    number_of_balconies = models.IntegerField(null=True)
    
    # Primary contact information
    primary_name = models.CharField(max_length=255, null=True)
    primary_number = models.CharField(max_length=11, unique=False, null=True)
    primary_email = models.EmailField(null=True)
    primary_relationship = models.CharField(max_length=50, null=True)
    
    # Secondary contact information
    secondary_name = models.CharField(max_length=255, null=True)
    secondary_number = models.CharField(max_length=11, unique=False, null=True)
    secondary_email = models.EmailField(null=True)
    secondary_relationship = models.CharField(max_length=50, null=True)
    
    # Emergency contact information
    emergency_name = models.CharField(max_length=255, null=True)
    emergency_number = models.CharField(max_length=11, unique=False, null=True)
    emergency_email = models.EmailField(null=True)
    emergency_relationship = models.CharField(max_length=50, null=True)

    def __str__(self):
        return f"Unit {self.unit_name} on Floor {self.floor.floor_no}"

class UnitDocs(models.Model):
    """
    Model for storing documents related to a Unit.
    """
    
    # The Unit to which the document belongs
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, related_name="docs")
    
    # FileField for uploading documents, using a custom upload path function
    unit_docs = models.FileField(
        upload_to=upload_to_unit_docs,
        null=True,
        validators=[FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png'])]
    )
    
    # User who created this document record
    created_by = models.ForeignKey(Member, null=True, on_delete=models.DO_NOTHING, related_name='created_unit_docs')
    created_at = models.DateTimeField(auto_now_add=True)
    
    # User who last updated this document record
    updated_by = models.ForeignKey(Member, null=True, on_delete=models.DO_NOTHING, related_name='updated_unit_docs')
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Doc for Unit {self.unit.unit_name}" 
    
class Resident(models.Model):
    member = models.ForeignKey(Member, on_delete=models.CASCADE)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True)  
    is_resident_or_tenant = models.BooleanField(default=True,
                                                help_text="True if resident, False if tenant")
    # unit_rent_fee = models.FloatField()
    # advance_payment = models.FloatField()
    unit_rent_fee = models.FloatField(default=0)
    advance_payment = models.FloatField(default=0)

    notice_period = models.IntegerField(help_text="Notice period in months")
    created_by = models.ForeignKey(Member, null=True, on_delete=models.DO_NOTHING, related_name='created_residents')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(Member, null=True, on_delete=models.DO_NOTHING, related_name='updated_residents')
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Resident: {self.member.full_name} in Unit {self.unit.unit_name}"

def upload_to_rental_docs(instance, filename):
    return os.path.join('resident_docs', f'{instance.resident.id}_{instance.resident.member.full_name}', filename)

class ResidentDocs(models.Model):
    resident = models.ForeignKey(
        'Resident', on_delete=models.CASCADE, related_name="docs"
    )
    rental_docs = models.FileField(
        upload_to=upload_to_rental_docs,
        null=True,
        validators=[FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png'])]
    )
    created_by = models.ForeignKey(
        Member, null=True, on_delete=models.DO_NOTHING, related_name='created_resident_docs'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        Member, null=True, on_delete=models.DO_NOTHING, related_name='updated_resident_docs'
    )
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Doc for {self.resident.member.full_name}"

class Owner(models.Model):
    member = models.ForeignKey(Member, on_delete=models.CASCADE)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE)
    ownership_transfer_from = models.ForeignKey(Member, null=True, blank=True, on_delete=models.SET_NULL, related_name='transferred_ownerships')
    ownership_percentage = models.DecimalField(max_digits=5, decimal_places=2)
    date_of_ownership = models.DateField()
    created_by = models.ForeignKey(Member, null=True, on_delete=models.DO_NOTHING, related_name='created_owners')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(Member, null=True, on_delete=models.DO_NOTHING, related_name='updated_owners')
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Owner: {self.member.full_name} in Unit {self.unit.unit_name}"
    
    def save(self, *args, **kwargs):
        # Check if this is a new instance (not yet saved to database)
        is_new = self.pk is None
        
        # Save the Owner object first
        super(Owner, self).save(*args, **kwargs)
        
        # If this is a new Owner object, update the member's community member status
        if is_new:
            self.member.is_comm_member = True
            self.member.comm_member_ever_created = True
            self.member.save()

def upload_to_owner_photo(instance, filename):
    # Generate a custom path for the image based on the instance's ID
    return os.path.join('owners', f'{instance.owner.member.id}_{instance.owner.member.full_name}', filename)

class OwnerDocs(models.Model):
    owner = models.ForeignKey(Owner, on_delete=models.CASCADE)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE)
    owner_docs = models.FileField(upload_to=upload_to_owner_photo, null=True, blank=True)
    created_by = models.ForeignKey(Member, null=True, on_delete=models.DO_NOTHING, related_name='created_owner_docs')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(Member, null=True, on_delete=models.DO_NOTHING, related_name='updated_owner_docs')
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Ownership documents for {self.owner.member.full_name}"


class UnitStaff(models.Model):
    from user.models import Member
    member = models.ForeignKey(Member, on_delete=models.CASCADE)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True)  
    unit_staff_status = models.BooleanField(default=True,
                                                help_text="True if Live-in, False if Part-time")

    created_by = models.ForeignKey(Member, null=True, on_delete=models.DO_NOTHING, related_name='created_unitstaff')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(Member, null=True, on_delete=models.DO_NOTHING, related_name='updated_unitstaff')
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"UnitStaff: {self.member.full_name} in Unit {self.unit.unit_name}"
    

