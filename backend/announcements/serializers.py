from rest_framework import serializers
from .models import Announcement, AnnouncementAttachment, AnnouncementHistory
from towers.models import Tower, Unit
from user.models import Member
import datetime


def make_json_serializable(value):
    """
    Convert non-JSON serializable objects to serializable representations
    """
    if isinstance(value, Member):
        return {
            'id': value.id,
            'full_name': value.full_name,
            'email': getattr(value, 'email', None)
        }
    elif isinstance(value, (datetime.datetime, datetime.date, datetime.time)):
        return value.isoformat()
    elif hasattr(value, '__dict__'):
        # For other model instances, return basic representation
        return {
            'id': getattr(value, 'id', None),
            'str': str(value)
        }
    else:
        return value


class AnnouncementAttachmentSerializer(serializers.ModelSerializer):
    """
    Serializer for announcement attachments
    """
    file_url = serializers.SerializerMethodField()

    class Meta:
        model = AnnouncementAttachment
        fields = ['id', 'file', 'file_url', 'file_name', 'file_type', 'file_size', 'created_at']
        read_only_fields = ['id', 'created_at', 'file_url']

    def get_file_url(self, obj):
        """
        Get the full URL for the file
        """
        if obj.file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file.url)
            return obj.file.url
        return None


class TowerSerializer(serializers.ModelSerializer):
    """
    Simple serializer for Tower data
    """
    
    class Meta:
        model = Tower
        fields = ['id', 'tower_name', 'tower_number']


class UnitSerializer(serializers.ModelSerializer):
    """
    Simple serializer for Unit data with tower information
    """
    tower_name = serializers.CharField(source='floor.tower.tower_name', read_only=True)
    tower_id = serializers.IntegerField(source='floor.tower.id', read_only=True)
    
    class Meta:
        model = Unit
        fields = ['id', 'unit_name', 'tower_id', 'tower_name']


class MemberSerializer(serializers.ModelSerializer):
    """
    Simple serializer for Member data
    """
    
    class Meta:
        model = Member
        fields = ['id', 'full_name', 'email', 'phone_number']


class AnnouncementHistorySerializer(serializers.ModelSerializer):
    """
    Serializer for announcement edit history
    """
    edited_by_name = serializers.CharField(source='edited_by.full_name', read_only=True)
    
    class Meta:
        model = AnnouncementHistory
        fields = ['id', 'edited_by', 'edited_by_name', 'edited_at', 'changes']
        read_only_fields = ['id', 'edited_at']


class AnnouncementSerializer(serializers.ModelSerializer):
    """
    Main serializer for Announcement model
    """
    attachments = serializers.SerializerMethodField()
    creator_name = serializers.CharField(source='creator.full_name', read_only=True)
    target_towers_data = TowerSerializer(source='target_towers', many=True, read_only=True)
    target_units_data = UnitSerializer(source='target_units', many=True, read_only=True)
    history = AnnouncementHistorySerializer(many=True, read_only=True)

    # Override label field to accept any custom text
    label = serializers.CharField(max_length=500, required=False, allow_blank=True)

    # For write operations
    target_tower_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    target_unit_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    
    class Meta:
        model = Announcement
        fields = [
            'id', 'title', 'description', 'creator', 'creator_name', 'post_as',
            'posted_group', 'posted_member', 'group_name', 'member_name',
            'priority', 'label', 'start_date', 'start_time', 'end_date', 'end_time',
            'status', 'views', 'is_pinned', 'manually_expired', 'created_at', 'updated_at',
            'attachments', 'target_towers_data', 'target_units_data', 'history',
            'target_tower_ids', 'target_unit_ids'
        ]
        read_only_fields = ['id', 'creator', 'status', 'views', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        # Extract tower and unit IDs
        target_tower_ids = validated_data.pop('target_tower_ids', [])
        target_unit_ids = validated_data.pop('target_unit_ids', [])
        
        # Set creator from request user
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            validated_data['creator'] = request.user.member
            validated_data['created_by'] = request.user.member

        # Handle post_as logic
        post_as = validated_data.get('post_as')
        if post_as == 'group':
            # Ensure posted_group is set and posted_member is None
            if not validated_data.get('posted_group'):
                raise serializers.ValidationError({'posted_group': 'Group is required when posting as a group'})
            # Store group name
            validated_data['group_name'] = validated_data['posted_group'].group_name
            validated_data['posted_member'] = None
            validated_data['member_name'] = None
        elif post_as == 'member':
            # Ensure posted_member is set and posted_group is None
            if not validated_data.get('posted_member'):
                raise serializers.ValidationError({'posted_member': 'Member is required when posting as a member'})
            # Store member name
            validated_data['member_name'] = validated_data['posted_member'].full_name
            validated_data['posted_group'] = None
            validated_data['group_name'] = None
        else:  # creator
            # Clear both group and member when posting as creator
            validated_data['posted_group'] = None
            validated_data['posted_member'] = None
            validated_data['group_name'] = None
            validated_data['member_name'] = None
        
        # Create announcement
        announcement = Announcement.objects.create(**validated_data)
        
        # Set many-to-many relationships
        if target_tower_ids:
            towers = Tower.objects.filter(id__in=target_tower_ids)
            announcement.target_towers.set(towers)
        
        if target_unit_ids:
            units = Unit.objects.filter(id__in=target_unit_ids)
            announcement.target_units.set(units)
        
        return announcement
    
    def update(self, instance, validated_data):
        # Extract tower and unit IDs
        target_tower_ids = validated_data.pop('target_tower_ids', None)
        target_unit_ids = validated_data.pop('target_unit_ids', None)

        # Handle post_as logic (same as create method)
        post_as = validated_data.get('post_as')
        if post_as == 'group':
            # Ensure posted_group is set and posted_member is None
            if not validated_data.get('posted_group'):
                raise serializers.ValidationError({'posted_group': 'Group is required when posting as a group'})
            # Store group name
            validated_data['group_name'] = validated_data['posted_group'].group_name
            validated_data['posted_member'] = None
            validated_data['member_name'] = None
        elif post_as == 'member':
            # Ensure posted_member is set and posted_group is None
            if not validated_data.get('posted_member'):
                raise serializers.ValidationError({'posted_member': 'Member is required when posting as a member'})
            # Store member name
            validated_data['member_name'] = validated_data['posted_member'].full_name
            validated_data['posted_group'] = None
            validated_data['group_name'] = None
        else:  # creator
            # Clear both group and member when posting as creator
            validated_data['posted_group'] = None
            validated_data['posted_member'] = None
            validated_data['group_name'] = None
            validated_data['member_name'] = None

        # Track changes for history
        changes = {}
        for field, value in validated_data.items():
            old_value = getattr(instance, field)
            if old_value != value:
                changes[field] = {
                    'old': make_json_serializable(old_value),
                    'new': make_json_serializable(value)
                }

        # Update announcement
        for field, value in validated_data.items():
            setattr(instance, field, value)
        
        # Set updated_by
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            instance.updated_by = request.user.member
        
        instance.save()
        
        # Update many-to-many relationships
        if target_tower_ids is not None:
            # Get old tower IDs for change tracking
            old_tower_ids = list(instance.target_towers.values_list('id', flat=True))
            towers = Tower.objects.filter(id__in=target_tower_ids)
            instance.target_towers.set(towers)
            if set(old_tower_ids) != set(target_tower_ids):
                changes['target_towers'] = {
                    'old': old_tower_ids,
                    'new': target_tower_ids
                }

        if target_unit_ids is not None:
            # Get old unit IDs for change tracking
            old_unit_ids = list(instance.target_units.values_list('id', flat=True))
            units = Unit.objects.filter(id__in=target_unit_ids)
            instance.target_units.set(units)
            if set(old_unit_ids) != set(target_unit_ids):
                changes['target_units'] = {
                    'old': old_unit_ids,
                    'new': target_unit_ids
                }
        
        # Create history record if there were changes
        if changes and request and hasattr(request, 'user'):
            AnnouncementHistory.objects.create(
                announcement=instance,
                edited_by=request.user.member,
                changes=changes
            )
        
        return instance

    def get_attachments(self, obj):
        """
        Get attachments with proper context for file URLs
        """
        attachments = obj.attachments.all()
        return AnnouncementAttachmentSerializer(
            attachments,
            many=True,
            context=self.context
        ).data


class AnnouncementListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for announcement list views
    """
    creator_name = serializers.CharField(source='creator.full_name', read_only=True)
    attachments = serializers.SerializerMethodField()
    attachment_count = serializers.SerializerMethodField()
    target_towers_count = serializers.SerializerMethodField()
    target_units_count = serializers.SerializerMethodField()
    target_units_data = UnitSerializer(source='target_units', many=True, read_only=True)

    class Meta:
        model = Announcement
        fields = [
            'id', 'title', 'description', 'creator_name', 'post_as',
            'priority', 'label', 'start_date', 'start_time', 'end_date', 'end_time',
            'status', 'views', 'is_pinned', 'manually_expired', 'created_at',
            'attachments', 'attachment_count', 'target_towers_count', 'target_units_count',
            'target_units_data'
        ]

    def get_attachments(self, obj):
        """
        Get attachments with proper context for file URLs
        """
        attachments = obj.attachments.all()
        return AnnouncementAttachmentSerializer(
            attachments,
            many=True,
            context=self.context
        ).data

    def get_attachment_count(self, obj):
        return obj.attachments.count()
    
    def get_target_towers_count(self, obj):
        return obj.target_towers.count()
    
    def get_target_units_count(self, obj):
        return obj.target_units.count()
