from rest_framework import status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, J<PERSON>NParser
from django.db.models import Q
from django.db import models
from django.utils import timezone
from django.core.files.base import ContentFile
import base64
import uuid
import json
from .models import Announcement, AnnouncementAttachment
from .serializers import (
    AnnouncementSerializer,
    AnnouncementListSerializer,
    AnnouncementAttachmentSerializer
)
from towers.models import Tower, Unit
from django.shortcuts import get_object_or_404


class AnnouncementListCreateView(APIView):
    permission_classes = [IsAuthenticated]
    parser_classes = [<PERSON><PERSON><PERSON><PERSON>ars<PERSON>, Form<PERSON>ars<PERSON>, J<PERSON>NPars<PERSON>]

    def get(self, request):
        queryset = Announcement.objects.all()
        status_filter = request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        search = request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(description__icontains=search) |
                Q(creator__full_name__icontains=search) |
                Q(posted_group__name__icontains=search) |
                Q(posted_member__full_name__icontains=search)
            )
        priority = request.query_params.get('priority', None)
        if priority:
            # Handle comma-separated priority values for OR filtering
            priority_list = [p.strip().lower() for p in priority.split(',') if p.strip()]
            if priority_list:
                queryset = queryset.filter(priority__in=priority_list)
        label = request.query_params.get('label', None)
        if label:
            # Handle comma-separated label values for OR filtering
            label_list = [l.strip() for l in label.split(',') if l.strip()]
            if label_list:
                queryset = queryset.filter(label__in=label_list)
        my_posts = request.query_params.get('my_posts', None)
        if my_posts and my_posts.lower() == 'true':
            queryset = queryset.filter(
                Q(creator=request.user.member) |
                Q(posted_member=request.user.member) |
                Q(posted_group__members=request.user.member)
            ).distinct()
        queryset = queryset.order_by('-is_pinned', '-created_at')
        queryset = queryset.select_related('creator', 'posted_group', 'posted_member').prefetch_related('attachments', 'target_towers', 'target_units', 'history')
        serializer = AnnouncementSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)

    def post(self, request):
        files = request.FILES.getlist('attachments')
        base64_attachments = request.data.get('base64_attachments', [])
        data = request.data.copy()
        post_as = data.get('post_as')
        if post_as == 'group':
            group_id = data.get('posted_group')
            if not group_id:
                return Response({'error': 'Validation failed', 'details': {'posted_group': 'Group is required when posting as a group'}}, status=status.HTTP_400_BAD_REQUEST)
            data['posted_group'] = group_id
            data['posted_member'] = None
        elif post_as == 'member':
            member_id = data.get('posted_member')
            if not member_id:
                return Response({'error': 'Validation failed', 'details': {'posted_member': 'Member is required when posting as a member'}}, status=status.HTTP_400_BAD_REQUEST)
            data['posted_member'] = member_id
            data['posted_group'] = None
        else:
            data['posted_group'] = None
            data['posted_member'] = None
        # Use the same process_ids function for consistency
        def process_ids(value):
            """Process tower/unit IDs from various formats to a flat list of integers"""
            result = []

            if isinstance(value, str):
                try:
                    # Try to parse as JSON first (from FormData JSON string)
                    parsed = json.loads(value)
                    if isinstance(parsed, list):
                        result = [int(id) for id in parsed if str(id).strip() and str(id).strip() != 'All' and str(id).strip().isdigit()]
                    else:
                        result = [int(parsed)] if str(parsed).strip() and str(parsed).strip() != 'All' and str(parsed).strip().isdigit() else []
                except (json.JSONDecodeError, ValueError):
                    # Handle comma-separated string
                    result = [int(id.strip()) for id in value.split(',') if id.strip() and id.strip() != 'All' and id.strip().isdigit()]
            elif isinstance(value, list):
                # Flatten nested lists and convert to integers
                for item in value:
                    if isinstance(item, list):
                        result.extend(process_ids(item))
                    elif isinstance(item, str):
                        try:
                            # Try to parse as JSON
                            parsed = json.loads(item)
                            if isinstance(parsed, list):
                                result.extend([int(id) for id in parsed if str(id).strip() and str(id).strip() != 'All' and str(id).strip().isdigit()])
                            else:
                                if str(parsed).strip() and str(parsed).strip() != 'All' and str(parsed).strip().isdigit():
                                    result.append(int(parsed))
                        except (json.JSONDecodeError, ValueError):
                            # Handle as regular string/number
                            if str(item).strip() and str(item).strip() != 'All' and str(item).strip().isdigit():
                                result.append(int(item))
                    elif str(item).strip() and str(item).strip() != 'All' and str(item).strip().isdigit():
                        result.append(int(item))
            elif isinstance(value, int):
                result = [value]

            return result

        if 'target_tower_ids' in data:
            data['target_tower_ids'] = process_ids(data.get('target_tower_ids'))
        if 'target_unit_ids' in data:
            data['target_unit_ids'] = process_ids(data.get('target_unit_ids'))

        serializer = AnnouncementSerializer(data=data, context={'request': request})
        if not serializer.is_valid():
            return Response({'error': 'Validation failed', 'details': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
        announcement = serializer.save(creator=request.user.member)
        for file in files:
            if not self._is_valid_file(file):
                continue
            AnnouncementAttachment.objects.create(
                announcement=announcement,
                file=file,
                file_name=file.name,
                file_type=file.content_type,
                file_size=file.size
            )
        for attachment_data in base64_attachments:
            try:
                self._create_attachment_from_base64(announcement, attachment_data)
            except Exception as e:
                print(f"Error processing base64 attachment: {e}")
                continue
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def _is_valid_file(self, file):
        if file.size > 5 * 1024 * 1024:
            return False
        allowed_types = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
            'image/bmp', 'image/tiff', 'image/svg+xml',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ]
        return file.content_type in allowed_types

    def _create_attachment_from_base64(self, announcement, attachment_data):
        try:
            print(f"Creating attachment from base64 for announcement {announcement.id}")
            print(f"Attachment data keys: {list(attachment_data.keys()) if isinstance(attachment_data, dict) else 'Not a dict'}")

            base64_string = attachment_data.get('base64', '')
            file_name = attachment_data.get('name', f'attachment_{uuid.uuid4().hex[:8]}')
            file_type = attachment_data.get('type', 'application/octet-stream')

            print(f"File name: {file_name}")
            print(f"File type: {file_type}")
            print(f"Base64 string length: {len(base64_string)}")

            if ',' in base64_string:
                base64_string = base64_string.split(',')[1]
                print(f"Cleaned base64 string length: {len(base64_string)}")

            file_data = base64.b64decode(base64_string)
            file_size = len(file_data)
            print(f"File size: {file_size} bytes")

            if file_size > 5 * 1024 * 1024:
                print("File size exceeds 5MB limit")
                return False

            django_file = ContentFile(file_data, name=file_name)
            attachment = AnnouncementAttachment.objects.create(
                announcement=announcement,
                file=django_file,
                file_name=file_name,
                file_type=file_type,
                file_size=file_size
            )
            print(f"Successfully created attachment with ID: {attachment.id}")
            return True
        except Exception as e:
            print(f"Error creating attachment from base64: {e}")
            import traceback
            traceback.print_exc()
            return False


class AnnouncementDetailView(APIView):
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser, JSONParser]

    def get_object(self, pk):
        return get_object_or_404(Announcement, pk=pk)

    def get(self, request, pk):
        announcement = self.get_object(pk)
        serializer = AnnouncementSerializer(announcement, context={'request': request})
        return Response(serializer.data)

    def put(self, request, pk):
        return self._update(request, pk, partial=False)

    def patch(self, request, pk):
        return self._update(request, pk, partial=True)

    def _update(self, request, pk, partial):
        announcement = self.get_object(pk)
        files = request.FILES.getlist('attachments')
        base64_attachments = request.data.get('base64_attachments', [])
        # Get attachments_to_delete as a list (FormData can send multiple values with same key)
        attachments_to_delete = request.data.getlist('attachments_to_delete')
        # Convert QueryDict to regular dict to avoid issues with list modifications
        data = dict(request.data)

        # Debug logging
        print(f"=== BACKEND UPDATE DEBUG ===")
        print(f"Announcement ID: {pk}")
        print(f"Files received: {len(files)}")
        print(f"Base64 attachments received: {len(base64_attachments)}")
        print(f"Attachments to delete: {attachments_to_delete} (type: {type(attachments_to_delete)}, length: {len(attachments_to_delete)})")
        print(f"Original data type: {type(request.data)}")
        print(f"Converted data type: {type(data)}")

        # Debug: Print original data for tower/unit IDs
        for field in ['target_tower_ids', 'target_unit_ids']:
            if field in data:
                print(f"Original {field}: {data[field]} (type: {type(data[field])})")

        # Flatten single-value lists in form data
        for key, value in list(data.items()):
            if key not in ['target_tower_ids', 'target_unit_ids', 'attachments_to_delete', 'base64_attachments']:
                if isinstance(value, list) and len(value) == 1:
                    data[key] = value[0]

        def process_ids(value):
            """Convert various ID formats to flat list of integers"""
            if not value:
                return []

            # Handle JSON string input
            if isinstance(value, str):
                try:
                    value = json.loads(value)
                except json.JSONDecodeError:
                    # Handle comma-separated string
                    return [int(id.strip()) for id in value.split(',') if id.strip().isdigit()]

            # Handle list input (including nested lists from FormData)
            if isinstance(value, list):
                ids = []
                for item in value:
                    if isinstance(item, list):
                        ids.extend(process_ids(item))
                    elif isinstance(item, str):
                        # Handle individual string values from FormData
                        if item.strip().isdigit():
                            ids.append(int(item))
                        else:
                            # Try comma-separated within the string
                            ids.extend([int(id.strip()) for id in item.split(',') if id.strip().isdigit()])
                    elif str(item).strip().isdigit():
                        ids.append(int(item))
                return ids

            # Handle single value
            if str(value).strip().isdigit():
                return [int(value)]

            return []

        # Process tower and unit IDs
        for field in ['target_tower_ids', 'target_unit_ids']:
            if field in data:
                data[field] = process_ids(data[field])
                print(f"Processed {field}: {data[field]}")

        # Debug: Print data being sent to serializer
        print(f"Data being sent to serializer:")
        for key, value in data.items():
            if key in ['target_tower_ids', 'target_unit_ids']:
                print(f"  {key}: {value} (type: {type(value)})")
                if isinstance(value, list) and len(value) > 0:
                    print(f"    First element: {value[0]} (type: {type(value[0])})")

        # Validate and save announcement
        serializer = AnnouncementSerializer(
            announcement,
            data=data,
            partial=partial,
            context={'request': request}
        )

        if not serializer.is_valid():
            print(f"Validation errors: {serializer.errors}")
            # Additional debug for serializer validation
            for field, errors in serializer.errors.items():
                if field in ['target_tower_ids', 'target_unit_ids']:
                    print(f"  Field {field} errors: {errors}")
                    print(f"  Field {field} value in serializer: {serializer.initial_data.get(field)}")
            return Response(
                {'error': 'Validation failed', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

        announcement = serializer.save()

        # Update relationships - always process these fields to allow clearing
        # If field is not present, treat as empty array to clear selections
        tower_ids = data.get('target_tower_ids', [])
        announcement.target_towers.set(Tower.objects.filter(id__in=tower_ids))

        unit_ids = data.get('target_unit_ids', [])
        announcement.target_units.set(Unit.objects.filter(id__in=unit_ids))

        # Handle attachments to delete
        print(f"Processing {len(attachments_to_delete)} attachments for deletion")
        for attachment_id in attachments_to_delete:
            print(f"Attempting to delete attachment ID: {attachment_id}")
            try:
                attachment = AnnouncementAttachment.objects.get(
                    id=attachment_id,
                    announcement=announcement
                )
                print(f"Found attachment: {attachment.file_name}, deleting...")
                attachment.file.delete(save=False)
                attachment.delete()
                print(f"Successfully deleted attachment ID: {attachment_id}")
            except AnnouncementAttachment.DoesNotExist:
                print(f"Attachment {attachment_id} not found for announcement {announcement.id}")

        # Add new file attachments
        for file in files:
            if file.size > 5 * 1024 * 1024:  # 5MB limit
                continue
            AnnouncementAttachment.objects.create(
                announcement=announcement,
                file=file,
                file_name=file.name,
                file_type=file.content_type,
                file_size=file.size
            )

        # Add base64 attachments
        for attachment_data in base64_attachments:
            try:
                file_name = attachment_data.get('name', 'attachment')
                file_data = attachment_data.get('data', '')
                if ',' in file_data:  # Remove data URI prefix if present
                    file_data = file_data.split(',')[1]
                file_content = base64.b64decode(file_data)

                django_file = ContentFile(file_content, name=file_name)
                AnnouncementAttachment.objects.create(
                    announcement=announcement,
                    file=django_file,
                    file_name=file_name,
                    file_type=attachment_data.get('type', 'application/octet-stream'),
                    file_size=len(file_content)
                )
            except Exception as e:
                print(f"Error processing base64 attachment: {e}")

        return Response(serializer.data)

    def delete(self, request, pk):
        announcement = self.get_object(pk)
        announcement.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    def _is_valid_file(self, file):
        """
        Validate file type and size for attachments
        """
        if file.size > 5 * 1024 * 1024:  # 5MB limit
            return False
        allowed_types = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
            'image/bmp', 'image/tiff', 'image/svg+xml',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ]
        return file.content_type in allowed_types

    def _create_attachment_from_base64(self, announcement, attachment_data):
        """
        Create attachment from base64 data
        """
        try:
            file_name = attachment_data.get('name', 'attachment')
            file_data = attachment_data.get('data', '')
            file_type = attachment_data.get('type', 'application/octet-stream')

            # Decode base64 data
            if ',' in file_data:
                file_data = file_data.split(',')[1]

            file_content = base64.b64decode(file_data)
            file_obj = ContentFile(file_content, name=file_name)

            # Create attachment
            attachment = AnnouncementAttachment.objects.create(
                announcement=announcement,
                file=file_obj,
                file_name=file_name,
                file_type=file_type,
                file_size=len(file_content)
            )
            return attachment
        except Exception as e:
            print(f"Error creating attachment from base64: {e}")
            raise


class AnnouncementByStatusView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        base_queryset = Announcement.objects.select_related('creator', 'posted_group', 'posted_member').prefetch_related('attachments', 'target_towers', 'target_units', 'history')
        ongoing = base_queryset.filter(status='ongoing')
        upcoming = base_queryset.filter(status='upcoming')
        expired = base_queryset.filter(status='expired')
        return Response({
            'ongoing': AnnouncementListSerializer(ongoing, many=True, context={'request': request}).data,
            'upcoming': AnnouncementListSerializer(upcoming, many=True, context={'request': request}).data,
            'expired': AnnouncementListSerializer(expired, many=True, context={'request': request}).data,
        })


class AnnouncementTogglePinView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request, pk):
        announcement = get_object_or_404(Announcement, pk=pk)
        announcement.is_pinned = not announcement.is_pinned
        announcement.save()
        return Response({'message': f'Announcement {"pinned" if announcement.is_pinned else "unpinned"} successfully', 'is_pinned': announcement.is_pinned})


class AnnouncementIncrementViewsView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request, pk):
        announcement = get_object_or_404(Announcement, pk=pk)
        announcement.views += 1
        announcement.save(update_fields=['views'])
        return Response({'views': announcement.views})


class AnnouncementForceExpireView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request, pk):
        announcement = get_object_or_404(Announcement, pk=pk)
        announcement.status = 'expired'
        announcement.manually_expired = True
        announcement.save(update_fields=['status', 'manually_expired'])
        return Response({'message': 'Announcement moved to expired status'})


class AnnouncementRestoreView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request, pk):
        announcement = get_object_or_404(Announcement, pk=pk)
        announcement.manually_expired = False
        announcement.update_status()
        announcement.save()
        return Response({'message': 'Announcement restored successfully', 'status': announcement.status})


class AnnouncementUpdateStatusesView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request):
        announcements = Announcement.objects.all()
        updated_count = 0
        for announcement in announcements:
            old_status = announcement.status
            announcement.update_status()
            if announcement.status != old_status:
                updated_count += 1
        return Response({'message': f'Updated {updated_count} announcement statuses', 'updated_count': updated_count})


class AnnouncementAttachmentListCreateView(APIView):
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser, JSONParser]
    def get(self, request):
        announcement_id = request.query_params.get('announcement_id', None)
        if announcement_id:
            attachments = AnnouncementAttachment.objects.filter(announcement_id=announcement_id)
        else:
            attachments = AnnouncementAttachment.objects.all()
        serializer = AnnouncementAttachmentSerializer(attachments, many=True, context={'request': request})
        return Response(serializer.data)
    def post(self, request):
        serializer = AnnouncementAttachmentSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            file = request.FILES.get('file')
            if file:
                serializer.save(file_name=file.name, file_type=file.content_type, file_size=file.size)
            else:
                serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AnnouncementAttachmentDetailView(APIView):
    permission_classes = [IsAuthenticated]
    def get_object(self, pk):
        return get_object_or_404(AnnouncementAttachment, pk=pk)
    def get(self, request, pk):
        attachment = self.get_object(pk)
        serializer = AnnouncementAttachmentSerializer(attachment, context={'request': request})
        return Response(serializer.data)
    def put(self, request, pk):
        attachment = self.get_object(pk)
        serializer = AnnouncementAttachmentSerializer(attachment, data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    def patch(self, request, pk):
        attachment = self.get_object(pk)
        serializer = AnnouncementAttachmentSerializer(attachment, data=request.data, partial=True, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    def delete(self, request, pk):
        attachment = self.get_object(pk)
        attachment.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class TowerListView(APIView):
    """
    API view to get all towers for announcement targeting
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        towers = Tower.objects.all().values('id', 'tower_name', 'tower_number')
        return Response(list(towers))


class UnitListView(APIView):
    """
    API view to get units, optionally filtered by tower IDs
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        tower_ids = request.query_params.get('tower_ids', None)

        if tower_ids:
            # Parse comma-separated tower IDs
            tower_id_list = [int(id.strip()) for id in tower_ids.split(',') if id.strip().isdigit()]
            units = Unit.objects.filter(floor__tower__id__in=tower_id_list)
        else:
            units = Unit.objects.all()

        # Get units with tower information
        units_data = []
        for unit in units.select_related('floor__tower'):
            units_data.append({
                'id': unit.id,
                'unit_name': unit.unit_name,
                'tower_id': unit.floor.tower.id,
                'tower_name': unit.floor.tower.tower_name,
            })

        return Response(units_data)


class BulkUserCountView(APIView):
    """
    API view to get user counts for multiple units in a single request
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        unit_ids = request.data.get('unit_ids', [])

        if not unit_ids:
            return Response({'error': 'unit_ids is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Import here to avoid circular imports
            from towers.models import Unit, Resident, Owner, UnitStaff

            # Get all units
            units = Unit.objects.filter(id__in=unit_ids)

            # Bulk fetch all data - only include active community members
            residents = Resident.objects.filter(
                unit__in=units,
                is_active=True,
                member__is_comm_member=True
            ).values('unit_id').annotate(count=models.Count('id'))
            owners = Owner.objects.filter(
                unit__in=units,
                member__is_comm_member=True  # Only include active community members
            ).values('unit_id').annotate(count=models.Count('id'))
            unit_staff = UnitStaff.objects.filter(
                unit__in=units,
                is_active=True,
                member__is_comm_member=True
            ).values('unit_id').annotate(count=models.Count('id'))

            # Create lookup dictionaries
            residents_count = {item['unit_id']: item['count'] for item in residents}
            owners_count = {item['unit_id']: item['count'] for item in owners}
            unit_staff_count = {item['unit_id']: item['count'] for item in unit_staff}

            # Calculate total counts for each unit
            result = {}
            for unit_id in unit_ids:
                total_count = (
                    residents_count.get(unit_id, 0) +
                    owners_count.get(unit_id, 0) +
                    unit_staff_count.get(unit_id, 0)
                )
                result[str(unit_id)] = total_count

            return Response(result)

        except Exception as e:
            return Response(
                {'error': f'Error calculating user counts: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AnnouncementLabelsView(APIView):
    """
    API view to get all unique labels from announcements
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Get all unique labels from announcements, excluding empty/null labels
        label_strings = Announcement.objects.exclude(
            Q(label__isnull=True) | Q(label__exact='')
        ).values_list('label', flat=True).distinct()

        # Split comma-separated labels and create a unique set
        unique_labels = set()
        for label_string in label_strings:
            if label_string:
                # Split by comma and strip whitespace from each label
                individual_labels = [label.strip() for label in label_string.split(',') if label.strip()]
                unique_labels.update(individual_labels)

        # Convert to sorted list
        sorted_labels = sorted(list(unique_labels))
        return Response(sorted_labels)
