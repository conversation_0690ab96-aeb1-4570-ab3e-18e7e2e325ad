# Generated by Django 5.2.1 on 2025-06-15 21:25

import announcements.models
import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('announcements', '0005_fix_label_field'),
        ('group_role', '0002_initial'),
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='announcement',
            name='posted_group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='group_announcements', to='group_role.group'),
        ),
        migrations.AddField(
            model_name='announcement',
            name='posted_member',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='member_announcements', to='user.member'),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='announcementattachment',
            name='file',
            field=models.FileField(upload_to=announcements.models.upload_to_announcement_attachments, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp', 'pdf', 'doc', 'docx'])]),
        ),
    ]
