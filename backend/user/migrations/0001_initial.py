# Generated by Django 5.0.4 on 2025-05-25 14:54

import django.core.validators
import django.db.models.deletion
import user.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('towers', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MemberType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type_name', models.CharField(max_length=128)),
            ],
        ),
        migrations.CreateModel(
            name='MyModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('age', models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name='Member',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=255)),
                ('general_contact', models.CharField(max_length=11)),
                ('general_email', models.EmailField(default='<EMAIL>', max_length=254)),
                ('login_email', models.EmailField(max_length=254, null=True, unique=True)),
                ('login_contact', models.CharField(max_length=11, null=True, unique=True)),
                ('nid_number', models.CharField(max_length=255, null=True, unique=True)),
                ('photo', models.ImageField(null=True, upload_to=user.models.upload_to_member_photo, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'heic'])])),
                ('photo_low_quality', models.ImageField(null=True, upload_to=user.models.upload_to_member_photo)),
                ('about_us', models.TextField(null=True)),
                ('facebook_profile', models.TextField(null=True)),
                ('linkedin_profile', models.TextField(null=True)),
                ('permanent_address', models.TextField(null=True)),
                ('present_address', models.TextField(null=True)),
                ('date_of_birth', models.DateField(null=True)),
                ('occupation', models.CharField(max_length=255, null=True)),
                ('gender', models.CharField(max_length=10, null=True)),
                ('marital_status', models.CharField(max_length=10, null=True)),
                ('religion', models.CharField(max_length=20, null=True)),
                ('nid_front', models.ImageField(null=True, upload_to=user.models.upload_to_member_photo, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'heic'])])),
                ('nid_back', models.ImageField(null=True, upload_to=user.models.upload_to_member_photo, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'heic'])])),
                ('is_org_member', models.BooleanField(default=0)),
                ('is_comm_member', models.BooleanField(default=0)),
                ('org_member_ever_created', models.BooleanField(default=0)),
                ('comm_member_ever_created', models.BooleanField(default=0)),
                ('is_first_login', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='members_created', to='user.member')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='members_updated', to='user.member')),
                ('user', models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('member_type', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='user.membertype')),
            ],
        ),
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(error_messages={'blank': 'Company name cannot be blank.', 'null': 'Company name cannot be null.', 'unique': 'This company name already exists.'}, max_length=255, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('unit', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='companies', to='towers.unit')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='company_created', to='user.member')),
                ('member', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='companies', to='user.member')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='company_updated', to='user.member')),
            ],
        ),
    ]
