from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Member,MyModel,MemberType,Company
from group_role.models import MembersRole,Role,GroupMembers
import random,string
from django.core.validators import RegexValidator
from datetime import date,datetime
from django.core.mail import send_mail
from django.conf import settings
from io import BytesIO
from PIL import Image
from django.core.files.uploadedfile import InMemoryUploadedFile
import sys
from django.core.exceptions import ValidationError
from rest_framework.response import Response
from rest_framework import status
from django.db import transaction
import os
from django.utils import timezone
from .models import Company
from user.models import Member 
from towers.models import Unit  
from django.db import IntegrityError
import json
VITE_BASE_API = os.getenv('VITE_BASE_API', 'http://localhost:5173')
login_link = f"{VITE_BASE_API}/login"

# Created by Ankan

class MemberTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = MemberType
        fields = '__all__'


# Function to generate a random username
def generate_random_username():
    # Try to ensure a unique username (you may need to fine-tune this)
    username = f"user{random.randint(1000, 9999)}"
    while User.objects.filter(username=username).exists():
        username = f"user{random.randint(1000, 9999)}"
    return username

# Function to generate a random password
def generate_random_password():
    length = 12  # Customize the length of the password as required
    characters = string.ascii_letters + string.digits + string.punctuation
    password = ''.join(random.choice(characters) for _ in range(length))
    return password

# User Serializer to handle the User creation logic
class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=False)  # password is optional during serialization
    username = serializers.CharField(required=False)
    class Meta:
        model = User
        fields = ['username', 'email', 'password']

    def create(self, validated_data):
        email = validated_data.get('email')  # The email is provided in the payload
        username = validated_data.get('username', generate_random_username())  # Auto-generate the username if not provided
        password = validated_data.get('password', generate_random_password())  # Generate password if not provided
        print('Username is:',username,'Password is:',password)
        # Create the user with the email, username, and password
        user = User.objects.create_user(username=username, email=email, password=password)
        return user,password,username

# MemberSerializer to handle creating both the User and Member models
class MemberSerializer(serializers.ModelSerializer):
      # Use the UserSerializer to create a nested user
    user = UserSerializer(required=False)
    photo = serializers.ImageField(required=False)
    nid_front = serializers.ImageField(required=False)
    nid_back = serializers.ImageField(required=False)
    members_role =serializers.ListField(required=False)
    
    date_of_birth = serializers.CharField(required=False)
    delivery_method = serializers.CharField(required=False)
    general_contact = serializers.CharField(
        validators=[
            RegexValidator(
                regex=r'^(018|019|013|017|015|016|014)\d{8}$',  # Corrected regex
                message="Contact number must be 11 digits and start with one of the allowed prefixes (018, 019, 013, 017, 015, 016, 014)."
            )
        ]
    )
    nid_number = serializers.CharField(
        required=False,
        allow_null=True,
        validators=[
            RegexValidator(
                regex=r'^\d{10}$|^\d{13}$|^\d{17}$',
                message="NID Number must be 10, 13, or 17 digits."
            )
        ]
    )

    photo_removed = serializers.CharField(required=False)
    nid_front_removed = serializers.CharField(required=False)
    nid_back_removed = serializers.CharField(required=False)
    delete_role = serializers.ListField(
        child=serializers.IntegerField(), required=False, write_only=True
    )
    def get_members_role(self, obj):
        """ Fetch all MembersRole objects related to this member. """
        members_roles = MembersRole.objects.filter(member=obj)
        return [
            {
                "id": mr.role.id,
                "role_name": mr.role.role_name,
                "is_member": mr.is_member,
                "is_group": mr.is_group,
            }
            for mr in members_roles
        ]
    def get_members_group(self, obj):
        """ Fetch all MembersRole objects related to this member. """
        members_group = GroupMembers.objects.filter(member=obj)
        return [
            {
                "group_name": mg.group.group_name,
            }
            for mg in members_group
        ]
    # def validate(self, data):
    #     nid = data.get('nid_number')
    #     # If we're updating (self.instance exists) and nid is provided,
    #     # make sure no other Member has the same NID
    #     if nid and self.instance:
    #         qs = Member.objects.filter(nid_number=nid).exclude(pk=self.instance.pk)
    #         if qs.exists():
    #             raise serializers.ValidationError({
    #                 "nid_number": "This NID number is already in use."
    #             })
    #     return data
      
    # Override the to_representation method to format the date_of_birth field
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        # Check if date_of_birth exists and is a date object
        if instance.date_of_birth:
            # Format the date to '19-Aug-1986'
            representation['date_of_birth'] = instance.date_of_birth.strftime('%d-%b-%Y')
        if instance.member_type:
            representation['member_type_name'] = instance.member_type.type_name
        if instance.user:
            representation['username'] = instance.user.username
        representation['member_roles'] = self.get_members_role(instance)
        representation['member_groups'] = self.get_members_group(instance)
         
        if instance.member_type:
                    representation['member_type_edit'] = {
                    'id': instance.member_type.id,
                    'member_type_name': instance.member_type.type_name
            }
        return representation
    
    class Meta:
        model = Member
        fields = [
            'id','user','member_type','members_role', 'full_name', 'general_contact','general_email','nid_number', 'photo','photo_low_quality',
            'about_us', 'facebook_profile', 'linkedin_profile', 'permanent_address', 
            'present_address', 'date_of_birth', 'occupation', 'gender', 'marital_status',
            'religion', 'nid_front', 'nid_back','is_org_member','is_comm_member','org_member_ever_created','comm_member_ever_created','delivery_method','login_email','login_contact','photo_removed','nid_front_removed','delete_role','nid_back_removed','is_first_login'
        ]  
        # depth =1 
    # def validate(self, data):
    
    #     if Member.objects.filter(
    #         full_name=data.get('full_name'),
    #         general_contact=data.get('general_contact'),
    #         general_email=data.get('general_email')
    #     ).exists():
    #         pass   

       
    #     return data
    
    def create(self, validated_data):
        
       
        with transaction.atomic():
            try:
                request = self.context.get('request')
            
                if not request:
                    raise serializers.ValidationError("Request object is missing from context")
                
                
                user_info = request.user  
                if not user_info.is_authenticated:
                    raise serializers.ValidationError("User is not authenticated")
                creator_member = Member.objects.get(user=user_info)

                user = UserSerializer()
                delivery_method = validated_data.pop('delivery_method',None) 
                role_ids = validated_data.pop('members_role',[])  # Get the list of role IDs
                date_of_birth = validated_data.get('date_of_birth')
                photo = validated_data.pop('photo',None)
                nid_front = validated_data.pop('nid_front',None)
                nid_back = validated_data.pop('nid_back',None)
            

                
                if date_of_birth:
                    # Parse the string into a datetime object
                    parsed_date = datetime.strptime(date_of_birth, '%d-%b-%Y')
                    # Convert it to a date object (this removes the time part)
                    validated_data['date_of_birth'] = parsed_date.date()
                # Create the user first
                # username =  generate_random_username()  # Auto-generate the username if not provided
                # password =  generate_random_password()  # Generate password if not provided
                # print('Username is:',username,'Password is:',password)
                # # Create the user with the email, username, and password
                # user = User.objects.create_user(username=username, password=password)

                # Now, create the Member instance and associate it with the created user
                member = Member.objects.create(**validated_data)
                member.created_by = creator_member
                member.created_at = timezone.now()
                member.nid_front = nid_front
                member.nid_back = nid_back
                member.is_first_login = True

                # Process the photo to create a lower resolution version if a photo was uploaded
                if photo:
                    try:
                        # Open the image using Pillow
                        image = Image.open(photo)
                        # Ensure the image is in RGB mode for consistent JPEG saving
                        if image.mode != 'RGB':
                            image = image.convert('RGB')
                    # Resize the image to exactly 150x150 pixels
                        image = image.resize((150, 150), Image.Resampling.LANCZOS)

                        
                        # Save the processed image to a BytesIO object
                        buffer = BytesIO()
                        image.save(buffer, format='JPEG')
                        buffer.seek(0)
                        
                        # Create an InMemoryUploadedFile to save into the model field
                        low_quality_image = InMemoryUploadedFile(
                            buffer,               # file
                            'ImageField',         # field name
                            f'low_quality_{photo.name}',  # filename
                            'image/jpeg',         # content type
                            buffer.tell(),        # size in bytes
                            None                  # charset
                        )
                        
                        # Save the low quality image to the member instance
                        member.photo_low_quality.save(f'low_quality_{photo.name}', low_quality_image, save=True)
                        member.photo = photo
                        
                    except Exception as e:
                        # Optionally, handle exceptions or log errors if image processing fails
                        raise serializers.ValidationError(f"Error occurred: {str(e)}")
                for role_id in role_ids:
                    role = Role.objects.get(id=role_id)
                    MembersRole.objects.create(member=member, role=role,is_member=True, is_group=False)
                
                # Send an email to the user with their username and password
                if delivery_method:
                    username =  generate_random_username()  # Auto-generate the username if not provided
                    password =  generate_random_password()  # Generate password if not provided
                    print('Username is:',username,'Password is:',password)
                    # Create the user with the email, username, and password
                    user = User.objects.create_user(username=username, password=password)
                    

                    member.user = user
                    if '@' in delivery_method:
                        print('test:',delivery_method, username, password)
                        self.send_welcome_email(delivery_method, username, password)
                        member.login_email = delivery_method
                        
                    elif delivery_method.isdigit():
                        contact_validator = RegexValidator(
                            regex=r'^(018|019|013|017|015|016|014)\d{8}$',
                            message="Contact number must be 11 digits and start with one of the allowed prefixes (018, 019, 013, 017, 015, 016, 014)."
                        )
                        try:
                            contact_validator(delivery_method)
                            member.login_contact = delivery_method
                            
                        except ValidationError:
                            raise serializers.ValidationError(
                                "Contact number must be 11 digits and start with one of the allowed prefixes (018, 019, 013, 017, 015, 016, 014)."
                            )                
                member.save()
                return member
            except Exception as e:
                    # If any error occurs, the transaction will be rolled back
                    raise serializers.ValidationError(f"Error occurred: {str(e)}")
    def update(self, instance, validated_data):
    # Start the transaction to ensure atomicity
        # print('update serializers')
        with transaction.atomic():
            try:
                request = self.context.get('request')
                # print(request)
                if not request:
                    raise serializers.ValidationError("Request object is missing from context")
                
                
                user_info = request.user  
                if not user_info.is_authenticated:
                    raise serializers.ValidationError("User is not authenticated")
                updated_by = Member.objects.get(user=user_info)
                # Handle fields to update
                photo = validated_data.get('photo')
                # nid_number = validated_data.get('nid_number', None)
                nid_number = validated_data.pop('nid_number', None)

                # Now normalize:
                if nid_number is not None:
                    # if they passed an empty string or all‐whitespace, we turn it into None
                    nid_number = nid_number.strip() or None
                    instance.nid_number = nid_number
                    
                # members_roles_data = validated_data.pop('members_role', None)
                # print('role_ids:',role_ids)
                date_of_birth = validated_data.get('date_of_birth', None)
                delivery_method = validated_data.get('delivery_method', None)
                # nid_front = validated_data.get('nid_front')
                # nid_back = validated_data.get('nid_back')

                photo_removed = validated_data.get('photo_removed')
                nid_front_removed = validated_data.get('nid_front_removed')
                nid_back_removed = validated_data.get('nid_back_removed')

                print(photo_removed,nid_front_removed,nid_back_removed)

                # print(photo,nid_front,nid_back)

                # If a new date of birth is provided, parse and update it
                if date_of_birth:
                    parsed_date = datetime.strptime(date_of_birth, '%d-%b-%Y')
                    validated_data['date_of_birth'] = parsed_date.date()

                # Update the instance with the validated data
                for attr, value in validated_data.items():
                    setattr(instance, attr, value)


                if nid_front_removed == 'Removed':
                    instance.nid_front = None
       
                if nid_back_removed == 'Removed' :
                    instance.nid_back = None
                    
                # Update the photo and its low-quality version if a new photo is provided
                if photo:
                    try:
                        # Open the new photo image using Pillow
                        image = Image.open(photo)
                        # Ensure the image is in RGB mode for consistent JPEG saving
                        if image.mode != 'RGB':
                            image = image.convert('RGB')
                        # Resize the image to 150x150 pixels for low quality
                        image = image.resize((150, 150), Image.Resampling.LANCZOS)

                        # Save the processed low-quality image to a buffer
                        buffer = BytesIO()
                        image.save(buffer, format='JPEG')
                        buffer.seek(0)

                        # Create an InMemoryUploadedFile to save into the model field
                        low_quality_image = InMemoryUploadedFile(
                            buffer,               # file
                            'ImageField',         # field name
                            f'low_quality_{photo.name}',  # filename
                            'image/jpeg',         # content type
                            buffer.tell(),        # size in bytes
                            None                  # charset
                        )

                        # Save the new low-quality image to the instance
                        instance.photo_low_quality.save(f'low_quality_{photo.name}', low_quality_image, save=False)
                        instance.photo = photo
                        
                    except Exception as e:
                        raise serializers.ValidationError(f"Error occurred: {str(e)}")
                if photo_removed == 'Removed':
                    instance.photo_low_quality = None
                    instance.photo = None
                  
                # # Update member roles if provided
                # if role_ids is not None:
                #     # First, clear the existing member roles
                #     # instance.member_roles.clear()
                #     MembersRole.objects.filter(member=instance).delete()
                #     # Then, create the new roles
                #     for role_id in role_ids:
                #         role = Role.objects.get(id=role_id)
                #         MembersRole.objects.create(member=instance, role=role)
                # else:
                #     if not MembersRole.objects.filter(member=instance).exists():
                #         # Member had no previous roles — nothing to preserve, just ensure it's clean
                #         MembersRole.objects.filter(member=instance).delete()


                # if role_ids is not None:
                #         MembersRole.objects.filter(member=instance).delete()
                #         MembersRole.objects.bulk_create([
                #             MembersRole(member=instance, role_id=role_id)
                #             for role_id in role_ids
                #         ])
                # else:
                #         MembersRole.objects.filter(member=instance).delete()
                # role_add_ids    = validated_data.pop('members_role', None)
                # role_delete_ids = validated_data.pop('delete_role',  None)
                # print('instance:',instance)
                

                # if role_delete_ids:
                #     MembersRole.objects.filter(
                #         member=instance,
                #         role_id__in=role_delete_ids
                #     ).delete()

                # if role_ids:
                #     existing = set(
                #         MembersRole.objects
                #                 .filter(member=instance)
                #                 .values_list('role_id', flat=True)
                #     )
                #     to_create = [rid for rid in role_ids if rid not in existing]
                #     MembersRole.objects.bulk_create([
                #         MembersRole(member=instance, role_id=rid)
                #         for rid in to_create
                #     ])
                    # Pop the members_role list (list of dicts with role_id, is_member, is_group)
                # members_roles_data = validated_data.pop('members_role', [])
                
                members_roles_data = validated_data.pop('members_role', [])
                role_delete_ids = validated_data.pop('delete_role', [])

                # Delete only roles with is_member=True
                if role_delete_ids:
                    MembersRole.objects.filter(
                        member=instance,
                        role_id__in=role_delete_ids,
                        is_member=True
                    ).delete()

                # Prevent duplicate creations (only is_member=True)
                existing_roles = MembersRole.objects.filter(
                    member=instance,
                    is_member=True
                ).values_list('role_id', flat=True)

                # Create new ones only if not already assigned
                for role_id in members_roles_data:
                    if role_id not in existing_roles:
                        MembersRole.objects.create(
                            member=instance,
                            role_id=role_id,
                            is_member=True,
                            is_group=False  # or True, based on your logic
                        )

              
                # Update delivery method if provided
                if delivery_method:
                    # Handle the delivery method: email or contact
                    user_created = 0
                    if instance.user is None:
                        username =  generate_random_username()  # Auto-generate the username if not provided
                        password =  generate_random_password()  # Generate password if not provided
                        print('Username is:',username,'Password is:',password)
                        # Create the user with the email, username, and password
                        user = User.objects.create_user(username=username, password=password)
                        instance.user = user
                        user_created = 1

                    if '@' in delivery_method:
                        instance.login_email = delivery_method
                        instance.login_contact = None
                        if user_created == 1:
                            self.send_welcome_email(delivery_method, username, password)
                        elif user_created == 0:
                            self.send_update_email(delivery_method,delivery_method)
                    elif delivery_method.isdigit():
                        contact_validator = RegexValidator(
                            regex=r'^(018|019|013|017|015|016|014)\d{8}$',
                            message="Contact number must be 11 digits and start with one of the allowed prefixes (018, 019, 013, 017, 015, 016, 014)."
                        )
                        try:
                            contact_validator(delivery_method)
                            instance.login_contact = delivery_method
                            instance.login_email = None
                            
                        except ValidationError:
                            raise serializers.ValidationError(
                                "Contact number must be 11 digits and start with one of the allowed prefixes (018, 019, 013, 017, 015, 016, 014)."
                            )
                # if not nid_number:
                #     instance.nid_number = None
                # else:
                #     instance.nid_number = nid_number
                # Save the updated member instance
                instance.updated_by = updated_by
                instance.updated_at = timezone.now()
                instance.save()
                return instance
            except Exception as e:
                # If any error occurs, the transaction will be rolled back
                raise serializers.ValidationError(f"Error occurred: {str(e)}")
    
    def send_welcome_email(self, recipient_email, username, password):
        subject = "Welcome to Our Platform"
        message = f"Dear User,\n\nYour account has been successfully created!\n\nUsername: {username}\nPassword: {password}\nLogin here: {login_link}\n\nPlease make sure to change your password after logging in.\n\nBest regards,\nThe Team"
            
        send_mail(
                subject,
                message,
                settings.EMAIL_HOST_USER,  # Sender email from settings
                [recipient_email],  # Recipient email
                fail_silently=False,
            )
    def send_update_email(self, recipient_email,delivery_method):
        subject = "Login Credential Updated"
        message = f"Dear User,\n\nYour login credential has been successfully updated!\n\nEmail or Contact: {delivery_method if '@' not in delivery_method else f'[{delivery_method}]'}\nLogin here: {login_link}\n\nBest regards,\nThe Team"

            
        send_mail(
                subject,
                message,
                settings.EMAIL_HOST_USER,  # Sender email from settings
                [recipient_email],  # Recipient email
                fail_silently=False,
            )
#created By Injam
# For setting New password for the first time user
class SetPasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(write_only=True, required=True)
    new_password = serializers.CharField(write_only=True, required=True)
    confirm_password = serializers.CharField(write_only=True, required=True)

    def validate_old_password(self, value):
        user_id = self.context.get('user_id')
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise serializers.ValidationError("User does not exist")
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect")
        return value

    def validate(self, data):
        if data.get('new_password') != data.get('confirm_password'):
            raise serializers.ValidationError("New password and confirmation do not match")
        return data


# dummy_code
class MyModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = MyModel
        fields = ['id', 'name', 'age']


class CompanySerializer(serializers.ModelSerializer):
    
    # member = MemberSerializer()
    class Meta:
        model = Company
        fields = ['company_name', 'member', 'unit', 'created_by']

   
class CompanySerializerlist(serializers.ModelSerializer):
    member = MemberSerializer()
    company_member_type = serializers.SerializerMethodField()

    class Meta:
        model = Company
        fields = ['company_name', 'member', 'company_member_type', 'unit', 'created_by']

    def get_company_member_type(self, obj):
        return "Owner"