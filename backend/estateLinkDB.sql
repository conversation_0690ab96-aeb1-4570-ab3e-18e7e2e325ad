/*
SQLyog Ultimate v12.5.1 (64 bit)
MySQL - 10.6.18-MariaDB-log : Database - estate_link-323038f725
*********************************************************************
*/

/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`estate_link-323038f725` /*!40100 DEFAULT CHARACTER SET latin1 COLLATE latin1_swedish_ci */;

USE `estate_link-323038f725`;

/*Table structure for table `audit_trail_audittrail` */

DROP TABLE IF EXISTS `audit_trail_audittrail`;

CREATE TABLE `audit_trail_audittrail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `event_type` varchar(21) NOT NULL,
  `table_name` varchar(50) NOT NULL,
  `row_id` bigint(20) NOT NULL,
  `old_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`old_data`)),
  `new_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`new_data`)),
  `description` longtext NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `member_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `audit_trail_audittrail_member_id_c39f942e_fk_user_member_id` (`member_id`),
  CONSTRAINT `audit_trail_audittrail_member_id_c39f942e_fk_user_member_id` FOREIGN KEY (`member_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `audit_trail_audittrail` */

/*Table structure for table `auth_group` */

DROP TABLE IF EXISTS `auth_group`;

CREATE TABLE `auth_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(150) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `auth_group` */

/*Table structure for table `auth_group_permissions` */

DROP TABLE IF EXISTS `auth_group_permissions`;

CREATE TABLE `auth_group_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_group_permissions_group_id_permission_id_0cd325b0_uniq` (`group_id`,`permission_id`),
  KEY `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` (`permission_id`),
  CONSTRAINT `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `auth_group_permissions_group_id_b120cbf9_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `auth_group_permissions` */

/*Table structure for table `auth_permission` */

DROP TABLE IF EXISTS `auth_permission`;

CREATE TABLE `auth_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `content_type_id` int(11) NOT NULL,
  `codename` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_permission_content_type_id_codename_01ab375a_uniq` (`content_type_id`,`codename`),
  CONSTRAINT `auth_permission_content_type_id_2f476e4b_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=117 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `auth_permission` */

insert  into `auth_permission`(`id`,`name`,`content_type_id`,`codename`) values 
(1,'Can add log entry',1,'add_logentry'),
(2,'Can change log entry',1,'change_logentry'),
(3,'Can delete log entry',1,'delete_logentry'),
(4,'Can view log entry',1,'view_logentry'),
(5,'Can add permission',2,'add_permission'),
(6,'Can change permission',2,'change_permission'),
(7,'Can delete permission',2,'delete_permission'),
(8,'Can view permission',2,'view_permission'),
(9,'Can add group',3,'add_group'),
(10,'Can change group',3,'change_group'),
(11,'Can delete group',3,'delete_group'),
(12,'Can view group',3,'view_group'),
(13,'Can add user',4,'add_user'),
(14,'Can change user',4,'change_user'),
(15,'Can delete user',4,'delete_user'),
(16,'Can view user',4,'view_user'),
(17,'Can add content type',5,'add_contenttype'),
(18,'Can change content type',5,'change_contenttype'),
(19,'Can delete content type',5,'delete_contenttype'),
(20,'Can view content type',5,'view_contenttype'),
(21,'Can add session',6,'add_session'),
(22,'Can change session',6,'change_session'),
(23,'Can delete session',6,'delete_session'),
(24,'Can view session',6,'view_session'),
(25,'Can add member type',7,'add_membertype'),
(26,'Can change member type',7,'change_membertype'),
(27,'Can delete member type',7,'delete_membertype'),
(28,'Can view member type',7,'view_membertype'),
(29,'Can add my model',8,'add_mymodel'),
(30,'Can change my model',8,'change_mymodel'),
(31,'Can delete my model',8,'delete_mymodel'),
(32,'Can view my model',8,'view_mymodel'),
(33,'Can add member',9,'add_member'),
(34,'Can change member',9,'change_member'),
(35,'Can delete member',9,'delete_member'),
(36,'Can view member',9,'view_member'),
(37,'Can add company',10,'add_company'),
(38,'Can change company',10,'change_company'),
(39,'Can delete company',10,'delete_company'),
(40,'Can view company',10,'view_company'),
(41,'Can add blacklisted token',11,'add_blacklistedtoken'),
(42,'Can change blacklisted token',11,'change_blacklistedtoken'),
(43,'Can delete blacklisted token',11,'delete_blacklistedtoken'),
(44,'Can view blacklisted token',11,'view_blacklistedtoken'),
(45,'Can add outstanding token',12,'add_outstandingtoken'),
(46,'Can change outstanding token',12,'change_outstandingtoken'),
(47,'Can delete outstanding token',12,'delete_outstandingtoken'),
(48,'Can view outstanding token',12,'view_outstandingtoken'),
(49,'Can add group',13,'add_group'),
(50,'Can change group',13,'change_group'),
(51,'Can delete group',13,'delete_group'),
(52,'Can view group',13,'view_group'),
(53,'Can add group members',14,'add_groupmembers'),
(54,'Can change group members',14,'change_groupmembers'),
(55,'Can delete group members',14,'delete_groupmembers'),
(56,'Can view group members',14,'view_groupmembers'),
(57,'Can add members role',15,'add_membersrole'),
(58,'Can change members role',15,'change_membersrole'),
(59,'Can delete members role',15,'delete_membersrole'),
(60,'Can view members role',15,'view_membersrole'),
(61,'Can add permission',16,'add_permission'),
(62,'Can change permission',16,'change_permission'),
(63,'Can delete permission',16,'delete_permission'),
(64,'Can view permission',16,'view_permission'),
(65,'Can add role',17,'add_role'),
(66,'Can change role',17,'change_role'),
(67,'Can delete role',17,'delete_role'),
(68,'Can view role',17,'view_role'),
(69,'Can add role group',18,'add_rolegroup'),
(70,'Can change role group',18,'change_rolegroup'),
(71,'Can delete role group',18,'delete_rolegroup'),
(72,'Can view role group',18,'view_rolegroup'),
(73,'Can add role permission',19,'add_rolepermission'),
(74,'Can change role permission',19,'change_rolepermission'),
(75,'Can delete role permission',19,'delete_rolepermission'),
(76,'Can view role permission',19,'view_rolepermission'),
(77,'Can add audit trail',20,'add_audittrail'),
(78,'Can change audit trail',20,'change_audittrail'),
(79,'Can delete audit trail',20,'delete_audittrail'),
(80,'Can view audit trail',20,'view_audittrail'),
(81,'Can add floor',21,'add_floor'),
(82,'Can change floor',21,'change_floor'),
(83,'Can delete floor',21,'delete_floor'),
(84,'Can view floor',21,'view_floor'),
(85,'Can add owner',22,'add_owner'),
(86,'Can change owner',22,'change_owner'),
(87,'Can delete owner',22,'delete_owner'),
(88,'Can view owner',22,'view_owner'),
(89,'Can add owner docs',23,'add_ownerdocs'),
(90,'Can change owner docs',23,'change_ownerdocs'),
(91,'Can delete owner docs',23,'delete_ownerdocs'),
(92,'Can view owner docs',23,'view_ownerdocs'),
(93,'Can add resident',24,'add_resident'),
(94,'Can change resident',24,'change_resident'),
(95,'Can delete resident',24,'delete_resident'),
(96,'Can view resident',24,'view_resident'),
(97,'Can add resident docs',25,'add_residentdocs'),
(98,'Can change resident docs',25,'change_residentdocs'),
(99,'Can delete resident docs',25,'delete_residentdocs'),
(100,'Can view resident docs',25,'view_residentdocs'),
(101,'Can add tower',26,'add_tower'),
(102,'Can change tower',26,'change_tower'),
(103,'Can delete tower',26,'delete_tower'),
(104,'Can view tower',26,'view_tower'),
(105,'Can add unit',27,'add_unit'),
(106,'Can change unit',27,'change_unit'),
(107,'Can delete unit',27,'delete_unit'),
(108,'Can view unit',27,'view_unit'),
(109,'Can add unit docs',28,'add_unitdocs'),
(110,'Can change unit docs',28,'change_unitdocs'),
(111,'Can delete unit docs',28,'delete_unitdocs'),
(112,'Can view unit docs',28,'view_unitdocs'),
(113,'Can add unit staff',29,'add_unitstaff'),
(114,'Can change unit staff',29,'change_unitstaff'),
(115,'Can delete unit staff',29,'delete_unitstaff'),
(116,'Can view unit staff',29,'view_unitstaff');

/*Table structure for table `auth_user` */

DROP TABLE IF EXISTS `auth_user`;

CREATE TABLE `auth_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `password` varchar(128) NOT NULL,
  `last_login` datetime(6) DEFAULT NULL,
  `is_superuser` tinyint(1) NOT NULL,
  `username` varchar(150) NOT NULL,
  `first_name` varchar(150) NOT NULL,
  `last_name` varchar(150) NOT NULL,
  `email` varchar(254) NOT NULL,
  `is_staff` tinyint(1) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `date_joined` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `auth_user` */

insert  into `auth_user`(`id`,`password`,`last_login`,`is_superuser`,`username`,`first_name`,`last_name`,`email`,`is_staff`,`is_active`,`date_joined`) values 
(6,'pbkdf2_sha256$720000$KLTVeX0UA2EZ6eWWOJmEUR$zHLMdFDTPjebM1J4mPhDsjFLSN+JgskQw/X4kzFD6cg=',NULL,0,'user6017','','','',0,1,'2025-03-02 08:51:01.669822'),
(7,'pbkdf2_sha256$720000$wfpZBhVCaJrWtMjlJFmavH$Slxg4x9FAmHQ2DWUzT1i1NrVUhveOyhBWefR76uywyM=',NULL,0,'user5464','','','',0,1,'2025-05-12 11:39:37.182907'),
(8,'pbkdf2_sha256$720000$ryTPeedBYhYJWqLnhrFG3M$vmWpLQZOInRoSrPTuvvkk5hfoen2vrxCaqakwXR2JRY=',NULL,0,'user4309','','','',0,1,'2025-05-12 12:17:28.459816'),
(9,'pbkdf2_sha256$720000$kqHA3qAjCngVtKVna7haL3$bIHu51hbNxbPAA0snXiON0xtPkDxs5yqzG6t9msvFv4=',NULL,0,'user7919','','','',0,1,'2025-05-12 12:28:00.332396'),
(10,'pbkdf2_sha256$720000$AFq7ky6I0ioDKMvFL9hQWS$2sIzhWdjqEn4IAs7RRJ8KhIblIPdKNsynrWas+1aQwI=',NULL,0,'user5646','','','',0,1,'2025-05-12 12:29:03.857211'),
(11,'pbkdf2_sha256$720000$FawsiKZhW6SSGXO4Zq0UKt$CHHL7+NuBjD3M306qJbTM6ysTcNNVes6vjQ/UKomYZE=',NULL,0,'user5254','','','',0,1,'2025-05-12 12:30:42.741259'),
(12,'pbkdf2_sha256$720000$DmW5SJ5pzMZv73bRJE9imV$dHyQjxIFlGwGuhVdshDn3StTDaD7/mhEYRD+foIjVfo=',NULL,0,'user9086','','','',0,1,'2025-05-12 12:38:07.156267'),
(13,'pbkdf2_sha256$720000$PpyzbPfjPAXdfORULKkyiK$Pc+u2PVvkvjM9MmEyQsjLRkGM+sARZwNWH6QiE3VNKY=',NULL,0,'user7164','','','',0,1,'2025-05-12 12:40:47.146116'),
(14,'pbkdf2_sha256$720000$Od848Y0w8fGKSGnnZ4vIXM$2TfgbfPqjr4i5ODO7eQ85HnJbevIqmpD17JbVygHR+4=',NULL,0,'user9851','','','',0,1,'2025-05-12 12:43:51.278562'),
(15,'pbkdf2_sha256$720000$VNXNU1PdbJPfQR9d4v1tFz$CwMhEpp6lqX55XMoBwBcm6LrPh674gD1kbUBBj6lR7E=',NULL,0,'user4105','','','',0,1,'2025-05-13 12:22:01.704254'),
(16,'pbkdf2_sha256$720000$Ym9Wn1LUTsDjYqo1GLuzh6$gTnT9OPYpN6tVkDV1SPVDXJnc6ikXA/paE1LjYG4fbc=',NULL,0,'user9637','','','',0,1,'2025-05-13 13:05:57.260642');

/*Table structure for table `auth_user_groups` */

DROP TABLE IF EXISTS `auth_user_groups`;

CREATE TABLE `auth_user_groups` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_user_groups_user_id_group_id_94350c0c_uniq` (`user_id`,`group_id`),
  KEY `auth_user_groups_group_id_97559544_fk_auth_group_id` (`group_id`),
  CONSTRAINT `auth_user_groups_group_id_97559544_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`),
  CONSTRAINT `auth_user_groups_user_id_6a12ed8b_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `auth_user_groups` */

/*Table structure for table `auth_user_user_permissions` */

DROP TABLE IF EXISTS `auth_user_user_permissions`;

CREATE TABLE `auth_user_user_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_user_user_permissions_user_id_permission_id_14a6b632_uniq` (`user_id`,`permission_id`),
  KEY `auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm` (`permission_id`),
  CONSTRAINT `auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `auth_user_user_permissions_user_id_a95ead1b_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `auth_user_user_permissions` */

/*Table structure for table `django_admin_log` */

DROP TABLE IF EXISTS `django_admin_log`;

CREATE TABLE `django_admin_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action_time` datetime(6) NOT NULL,
  `object_id` longtext DEFAULT NULL,
  `object_repr` varchar(200) NOT NULL,
  `action_flag` smallint(5) unsigned NOT NULL CHECK (`action_flag` >= 0),
  `change_message` longtext NOT NULL,
  `content_type_id` int(11) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `django_admin_log_content_type_id_c4bce8eb_fk_django_co` (`content_type_id`),
  KEY `django_admin_log_user_id_c564eba6_fk_auth_user_id` (`user_id`),
  CONSTRAINT `django_admin_log_content_type_id_c4bce8eb_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`),
  CONSTRAINT `django_admin_log_user_id_c564eba6_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `django_admin_log` */

/*Table structure for table `django_content_type` */

DROP TABLE IF EXISTS `django_content_type`;

CREATE TABLE `django_content_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_label` varchar(100) NOT NULL,
  `model` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `django_content_type_app_label_model_76bd3d3b_uniq` (`app_label`,`model`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `django_content_type` */

insert  into `django_content_type`(`id`,`app_label`,`model`) values 
(1,'admin','logentry'),
(20,'audit_trail','audittrail'),
(3,'auth','group'),
(2,'auth','permission'),
(4,'auth','user'),
(5,'contenttypes','contenttype'),
(13,'group_role','group'),
(14,'group_role','groupmembers'),
(15,'group_role','membersrole'),
(16,'group_role','permission'),
(17,'group_role','role'),
(18,'group_role','rolegroup'),
(19,'group_role','rolepermission'),
(6,'sessions','session'),
(11,'token_blacklist','blacklistedtoken'),
(12,'token_blacklist','outstandingtoken'),
(21,'towers','floor'),
(22,'towers','owner'),
(23,'towers','ownerdocs'),
(24,'towers','resident'),
(25,'towers','residentdocs'),
(26,'towers','tower'),
(27,'towers','unit'),
(28,'towers','unitdocs'),
(29,'towers','unitstaff'),
(10,'user','company'),
(9,'user','member'),
(7,'user','membertype'),
(8,'user','mymodel');

/*Table structure for table `django_migrations` */

DROP TABLE IF EXISTS `django_migrations`;

CREATE TABLE `django_migrations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `applied` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `django_migrations` */

insert  into `django_migrations`(`id`,`app`,`name`,`applied`) values 
(1,'contenttypes','0001_initial','2025-05-25 14:54:54.505735'),
(2,'auth','0001_initial','2025-05-25 14:54:58.566168'),
(3,'admin','0001_initial','2025-05-25 14:54:59.560087'),
(4,'admin','0002_logentry_remove_auto_add','2025-05-25 14:54:59.736417'),
(5,'admin','0003_logentry_add_action_flag_choices','2025-05-25 14:54:59.910001'),
(6,'towers','0001_initial','2025-05-25 14:55:01.586309'),
(7,'user','0001_initial','2025-05-25 14:55:04.880250'),
(8,'audit_trail','0001_initial','2025-05-25 14:55:05.201649'),
(9,'audit_trail','0002_initial','2025-05-25 14:55:05.699884'),
(10,'contenttypes','0002_remove_content_type_name','2025-05-25 14:55:06.691439'),
(11,'auth','0002_alter_permission_name_max_length','2025-05-25 14:55:07.023781'),
(12,'auth','0003_alter_user_email_max_length','2025-05-25 14:55:07.356712'),
(13,'auth','0004_alter_user_username_opts','2025-05-25 14:55:07.526262'),
(14,'auth','0005_alter_user_last_login_null','2025-05-25 14:55:07.856713'),
(15,'auth','0006_require_contenttypes_0002','2025-05-25 14:55:08.016251'),
(16,'auth','0007_alter_validators_add_error_messages','2025-05-25 14:55:08.184134'),
(17,'auth','0008_alter_user_username_max_length','2025-05-25 14:55:08.512269'),
(18,'auth','0009_alter_user_last_name_max_length','2025-05-25 14:55:08.840372'),
(19,'auth','0010_alter_group_name_max_length','2025-05-25 14:55:09.178966'),
(20,'auth','0011_update_proxy_permissions','2025-05-25 14:55:09.839229'),
(21,'auth','0012_alter_user_first_name_max_length','2025-05-25 14:55:10.169514'),
(22,'group_role','0001_initial','2025-05-25 14:55:11.494805'),
(23,'group_role','0002_initial','2025-05-25 14:55:18.526372'),
(24,'sessions','0001_initial','2025-05-25 14:55:19.180792'),
(25,'token_blacklist','0001_initial','2025-05-25 14:55:20.196324'),
(26,'token_blacklist','0002_outstandingtoken_jti_hex','2025-05-25 14:55:20.527671'),
(27,'token_blacklist','0003_auto_20171017_2007','2025-05-25 14:55:21.353571'),
(28,'token_blacklist','0004_auto_20171017_2013','2025-05-25 14:55:21.871992'),
(29,'token_blacklist','0005_remove_outstandingtoken_jti','2025-05-25 14:55:22.980446'),
(30,'token_blacklist','0006_auto_20171017_2113','2025-05-25 14:55:23.329426'),
(31,'token_blacklist','0007_auto_20171017_2214','2025-05-25 14:55:25.434459'),
(32,'token_blacklist','0008_migrate_to_bigautofield','2025-05-25 14:55:27.674451'),
(33,'token_blacklist','0010_fix_migrate_to_bigautofield','2025-05-25 14:55:27.874933'),
(34,'token_blacklist','0011_linearizes_history','2025-05-25 14:55:28.042461'),
(35,'token_blacklist','0012_alter_outstandingtoken_user','2025-05-25 14:55:28.226056'),
(36,'towers','0002_initial','2025-05-25 14:55:39.707145');

/*Table structure for table `django_session` */

DROP TABLE IF EXISTS `django_session`;

CREATE TABLE `django_session` (
  `session_key` varchar(40) NOT NULL,
  `session_data` longtext NOT NULL,
  `expire_date` datetime(6) NOT NULL,
  PRIMARY KEY (`session_key`),
  KEY `django_session_expire_date_a5c62663` (`expire_date`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `django_session` */

/*Table structure for table `group_role_group` */

DROP TABLE IF EXISTS `group_role_group`;

CREATE TABLE `group_role_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) NOT NULL,
  `group_description` longtext DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `group_name` (`group_name`),
  KEY `group_role_group_created_by_id_79f42542_fk_user_member_id` (`created_by_id`),
  KEY `group_role_group_updated_by_id_e07cfae8_fk_user_member_id` (`updated_by_id`),
  CONSTRAINT `group_role_group_created_by_id_79f42542_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `group_role_group_updated_by_id_e07cfae8_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `group_role_group` */

/*Table structure for table `group_role_groupmembers` */

DROP TABLE IF EXISTS `group_role_groupmembers`;

CREATE TABLE `group_role_groupmembers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `group_id` bigint(20) NOT NULL,
  `member_id` bigint(20) NOT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `group_role_groupmembers_created_by_id_d0b58b3d_fk_user_member_id` (`created_by_id`),
  KEY `group_role_groupmembers_group_id_cab1a6ab_fk_group_role_group_id` (`group_id`),
  KEY `group_role_groupmembers_member_id_844e69a6_fk_user_member_id` (`member_id`),
  KEY `group_role_groupmembers_updated_by_id_71a76b1c_fk_user_member_id` (`updated_by_id`),
  CONSTRAINT `group_role_groupmembers_created_by_id_d0b58b3d_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `group_role_groupmembers_group_id_cab1a6ab_fk_group_role_group_id` FOREIGN KEY (`group_id`) REFERENCES `group_role_group` (`id`),
  CONSTRAINT `group_role_groupmembers_member_id_844e69a6_fk_user_member_id` FOREIGN KEY (`member_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `group_role_groupmembers_updated_by_id_71a76b1c_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `group_role_groupmembers` */

/*Table structure for table `group_role_membersrole` */

DROP TABLE IF EXISTS `group_role_membersrole`;

CREATE TABLE `group_role_membersrole` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `is_active` tinyint(1) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `member_id` bigint(20) NOT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  `role_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `group_role_membersrole_created_by_id_8e99d1e8_fk_user_member_id` (`created_by_id`),
  KEY `group_role_membersrole_member_id_0ab6c7ee_fk_user_member_id` (`member_id`),
  KEY `group_role_membersrole_updated_by_id_e0854389_fk_user_member_id` (`updated_by_id`),
  KEY `group_role_membersrole_role_id_b4d96736_fk_group_role_role_id` (`role_id`),
  CONSTRAINT `group_role_membersrole_created_by_id_8e99d1e8_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `group_role_membersrole_member_id_0ab6c7ee_fk_user_member_id` FOREIGN KEY (`member_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `group_role_membersrole_role_id_b4d96736_fk_group_role_role_id` FOREIGN KEY (`role_id`) REFERENCES `group_role_role` (`id`),
  CONSTRAINT `group_role_membersrole_updated_by_id_e0854389_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `group_role_membersrole` */

insert  into `group_role_membersrole`(`id`,`is_active`,`created_at`,`updated_at`,`created_by_id`,`member_id`,`updated_by_id`,`role_id`) values 
(42,1,'2025-05-12 15:14:02.839880','2025-05-12 15:14:02.839880',NULL,6,NULL,2);

/*Table structure for table `group_role_permission` */

DROP TABLE IF EXISTS `group_role_permission`;

CREATE TABLE `group_role_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `permission_name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `group_role_permission` */

insert  into `group_role_permission`(`id`,`permission_name`) values 
(1,'Create Member'),
(2,'Edit Member'),
(3,'View Member List'),
(4,'Create Role'),
(5,'Edit Role'),
(6,'View Role List'),
(7,'Create Group'),
(8,'Edit Group'),
(9,'View Group List'),
(10,'Create Tower'),
(11,'Edit Tower'),
(12,'View Tower');

/*Table structure for table `group_role_role` */

DROP TABLE IF EXISTS `group_role_role`;

CREATE TABLE `group_role_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(255) NOT NULL,
  `role_description` longtext NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_name` (`role_name`),
  KEY `group_role_role_created_by_id_b91942a5_fk_user_member_id` (`created_by_id`),
  KEY `group_role_role_updated_by_id_b99ac99e_fk_user_member_id` (`updated_by_id`),
  CONSTRAINT `group_role_role_created_by_id_b91942a5_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `group_role_role_updated_by_id_b99ac99e_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `group_role_role` */

insert  into `group_role_role`(`id`,`role_name`,`role_description`,`is_active`,`created_at`,`updated_at`,`created_by_id`,`updated_by_id`) values 
(1,'Staff','Staff',1,'2025-03-16 11:32:12.783592','2025-03-20 14:29:09.889385',NULL,6),
(2,'Admin','Admin',1,'2025-03-16 11:32:12.783592','2025-03-19 13:10:45.879205',NULL,6);

/*Table structure for table `group_role_rolegroup` */

DROP TABLE IF EXISTS `group_role_rolegroup`;

CREATE TABLE `group_role_rolegroup` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `is_active` tinyint(1) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `group_id` bigint(20) NOT NULL,
  `role_id` bigint(20) NOT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `group_role_rolegroup_created_by_id_3d9f042c_fk_user_member_id` (`created_by_id`),
  KEY `group_role_rolegroup_group_id_db467b7d_fk_group_role_group_id` (`group_id`),
  KEY `group_role_rolegroup_role_id_a8581610_fk_group_role_role_id` (`role_id`),
  KEY `group_role_rolegroup_updated_by_id_d0202152_fk_user_member_id` (`updated_by_id`),
  CONSTRAINT `group_role_rolegroup_created_by_id_3d9f042c_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `group_role_rolegroup_group_id_db467b7d_fk_group_role_group_id` FOREIGN KEY (`group_id`) REFERENCES `group_role_group` (`id`),
  CONSTRAINT `group_role_rolegroup_role_id_a8581610_fk_group_role_role_id` FOREIGN KEY (`role_id`) REFERENCES `group_role_role` (`id`),
  CONSTRAINT `group_role_rolegroup_updated_by_id_d0202152_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `group_role_rolegroup` */

/*Table structure for table `group_role_rolepermission` */

DROP TABLE IF EXISTS `group_role_rolepermission`;

CREATE TABLE `group_role_rolepermission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `is_active` tinyint(1) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `permission_id` bigint(20) NOT NULL,
  `role_id` bigint(20) NOT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `group_role_rolepermi_created_by_id_fb878b81_fk_user_memb` (`created_by_id`),
  KEY `group_role_rolepermi_permission_id_296d1f48_fk_group_rol` (`permission_id`),
  KEY `group_role_rolepermission_role_id_6ce3889b_fk_group_role_role_id` (`role_id`),
  KEY `group_role_rolepermi_updated_by_id_2522ff3c_fk_user_memb` (`updated_by_id`),
  CONSTRAINT `group_role_rolepermi_created_by_id_fb878b81_fk_user_memb` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `group_role_rolepermi_permission_id_296d1f48_fk_group_rol` FOREIGN KEY (`permission_id`) REFERENCES `group_role_permission` (`id`),
  CONSTRAINT `group_role_rolepermi_updated_by_id_2522ff3c_fk_user_memb` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `group_role_rolepermission_role_id_6ce3889b_fk_group_role_role_id` FOREIGN KEY (`role_id`) REFERENCES `group_role_role` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `group_role_rolepermission` */

insert  into `group_role_rolepermission`(`id`,`is_active`,`created_at`,`updated_at`,`created_by_id`,`permission_id`,`role_id`,`updated_by_id`) values 
(22,1,'2025-03-19 13:10:45.882948','2025-03-19 13:10:45.882948',NULL,1,2,6),
(23,1,'2025-03-19 13:10:45.882948','2025-03-19 13:10:45.884954',NULL,2,2,6),
(24,1,'2025-03-19 13:10:45.884954','2025-03-19 13:10:45.884954',NULL,3,2,6),
(25,1,'2025-03-19 13:10:45.886959','2025-03-19 13:10:45.886959',NULL,4,2,6),
(26,1,'2025-03-19 13:10:45.886959','2025-03-19 13:10:45.886959',NULL,5,2,6),
(27,1,'2025-03-19 13:10:45.886959','2025-03-19 13:10:45.886959',NULL,6,2,6),
(28,1,'2025-03-19 13:10:45.888963','2025-03-19 13:10:45.888963',NULL,7,2,6),
(29,1,'2025-03-19 13:10:45.888963','2025-03-19 13:10:45.888963',NULL,8,2,6),
(30,1,'2025-03-19 13:10:45.890966','2025-03-19 13:10:45.890966',NULL,9,2,6),
(31,1,'2025-03-19 13:10:45.890966','2025-03-19 13:10:45.890966',NULL,10,2,6),
(32,1,'2025-03-19 13:10:45.892969','2025-03-19 13:10:45.892969',NULL,11,2,6),
(33,1,'2025-03-19 13:10:45.894118','2025-03-19 13:10:45.894118',NULL,12,2,6);

/*Table structure for table `token_blacklist_blacklistedtoken` */

DROP TABLE IF EXISTS `token_blacklist_blacklistedtoken`;

CREATE TABLE `token_blacklist_blacklistedtoken` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `blacklisted_at` datetime(6) NOT NULL,
  `token_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token_id` (`token_id`),
  CONSTRAINT `token_blacklist_blacklistedtoken_token_id_3cc7fe56_fk` FOREIGN KEY (`token_id`) REFERENCES `token_blacklist_outstandingtoken` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `token_blacklist_blacklistedtoken` */

/*Table structure for table `token_blacklist_outstandingtoken` */

DROP TABLE IF EXISTS `token_blacklist_outstandingtoken`;

CREATE TABLE `token_blacklist_outstandingtoken` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `token` longtext NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `expires_at` datetime(6) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `jti` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token_blacklist_outstandingtoken_jti_hex_d9bdf6f7_uniq` (`jti`),
  KEY `token_blacklist_outs_user_id_83bc629a_fk_auth_user` (`user_id`),
  CONSTRAINT `token_blacklist_outs_user_id_83bc629a_fk_auth_user` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `token_blacklist_outstandingtoken` */

insert  into `token_blacklist_outstandingtoken`(`id`,`token`,`created_at`,`expires_at`,`user_id`,`jti`) values 
(1,'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc0OTAyOTMyMSwiaWF0IjoxNzQ4MTY1MzIxLCJqdGkiOiJkZWYyNjU0NTBmZDk0ZTlhOTIzNjM4ZTc3M2IxYTM3YyIsInVzZXJfaWQiOjZ9.2GUCQk2nCdCr_iGYi4o1NSoG9LAY-xRZhkrFxIHmjBI','2025-05-25 09:28:41.644651','2025-06-04 09:28:41.000000',6,'def265450fd94e9a923638e773b1a37c');

/*Table structure for table `towers_floor` */

DROP TABLE IF EXISTS `towers_floor`;

CREATE TABLE `towers_floor` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `floor_no` int(11) NOT NULL,
  `number_of_units` int(11) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  `tower_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `towers_floor_created_by_id_3141f1c1_fk_user_member_id` (`created_by_id`),
  KEY `towers_floor_updated_by_id_f1a1faad_fk_user_member_id` (`updated_by_id`),
  KEY `towers_floor_tower_id_65841d8f_fk_towers_tower_id` (`tower_id`),
  CONSTRAINT `towers_floor_created_by_id_3141f1c1_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `towers_floor_tower_id_65841d8f_fk_towers_tower_id` FOREIGN KEY (`tower_id`) REFERENCES `towers_tower` (`id`),
  CONSTRAINT `towers_floor_updated_by_id_f1a1faad_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `towers_floor` */

/*Table structure for table `towers_owner` */

DROP TABLE IF EXISTS `towers_owner`;

CREATE TABLE `towers_owner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ownership_percentage` decimal(5,2) NOT NULL,
  `date_of_ownership` date NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `member_id` bigint(20) NOT NULL,
  `ownership_transfer_from_id` bigint(20) DEFAULT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  `unit_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `towers_owner_created_by_id_7266160b_fk_user_member_id` (`created_by_id`),
  KEY `towers_owner_member_id_e4f6245b_fk_user_member_id` (`member_id`),
  KEY `towers_owner_ownership_transfer_f_f407db9b_fk_user_memb` (`ownership_transfer_from_id`),
  KEY `towers_owner_updated_by_id_7672abcc_fk_user_member_id` (`updated_by_id`),
  KEY `towers_owner_unit_id_664730fb_fk_towers_unit_id` (`unit_id`),
  CONSTRAINT `towers_owner_created_by_id_7266160b_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `towers_owner_member_id_e4f6245b_fk_user_member_id` FOREIGN KEY (`member_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `towers_owner_ownership_transfer_f_f407db9b_fk_user_memb` FOREIGN KEY (`ownership_transfer_from_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `towers_owner_unit_id_664730fb_fk_towers_unit_id` FOREIGN KEY (`unit_id`) REFERENCES `towers_unit` (`id`),
  CONSTRAINT `towers_owner_updated_by_id_7672abcc_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `towers_owner` */

/*Table structure for table `towers_ownerdocs` */

DROP TABLE IF EXISTS `towers_ownerdocs`;

CREATE TABLE `towers_ownerdocs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `owner_docs` varchar(100) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `owner_id` bigint(20) NOT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  `unit_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `towers_ownerdocs_created_by_id_ec311952_fk_user_member_id` (`created_by_id`),
  KEY `towers_ownerdocs_owner_id_a5eb4eeb_fk_towers_owner_id` (`owner_id`),
  KEY `towers_ownerdocs_updated_by_id_f410c0d2_fk_user_member_id` (`updated_by_id`),
  KEY `towers_ownerdocs_unit_id_7727b47b_fk_towers_unit_id` (`unit_id`),
  CONSTRAINT `towers_ownerdocs_created_by_id_ec311952_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `towers_ownerdocs_owner_id_a5eb4eeb_fk_towers_owner_id` FOREIGN KEY (`owner_id`) REFERENCES `towers_owner` (`id`),
  CONSTRAINT `towers_ownerdocs_unit_id_7727b47b_fk_towers_unit_id` FOREIGN KEY (`unit_id`) REFERENCES `towers_unit` (`id`),
  CONSTRAINT `towers_ownerdocs_updated_by_id_f410c0d2_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `towers_ownerdocs` */

/*Table structure for table `towers_resident` */

DROP TABLE IF EXISTS `towers_resident`;

CREATE TABLE `towers_resident` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `is_active` tinyint(1) NOT NULL,
  `is_resident_or_tenant` tinyint(1) NOT NULL,
  `unit_rent_fee` double NOT NULL,
  `advance_payment` double NOT NULL,
  `notice_period` int(11) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `member_id` bigint(20) NOT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  `unit_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `towers_resident_created_by_id_0c635f68_fk_user_member_id` (`created_by_id`),
  KEY `towers_resident_member_id_51c4841a_fk_user_member_id` (`member_id`),
  KEY `towers_resident_updated_by_id_96fdd873_fk_user_member_id` (`updated_by_id`),
  KEY `towers_resident_unit_id_376da84b_fk_towers_unit_id` (`unit_id`),
  CONSTRAINT `towers_resident_created_by_id_0c635f68_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `towers_resident_member_id_51c4841a_fk_user_member_id` FOREIGN KEY (`member_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `towers_resident_unit_id_376da84b_fk_towers_unit_id` FOREIGN KEY (`unit_id`) REFERENCES `towers_unit` (`id`),
  CONSTRAINT `towers_resident_updated_by_id_96fdd873_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `towers_resident` */

/*Table structure for table `towers_residentdocs` */

DROP TABLE IF EXISTS `towers_residentdocs`;

CREATE TABLE `towers_residentdocs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `rental_docs` varchar(100) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `resident_id` bigint(20) NOT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `towers_residentdocs_created_by_id_cd1099d9_fk_user_member_id` (`created_by_id`),
  KEY `towers_residentdocs_resident_id_0eb0f5c6_fk_towers_resident_id` (`resident_id`),
  KEY `towers_residentdocs_updated_by_id_f9901b3a_fk_user_member_id` (`updated_by_id`),
  CONSTRAINT `towers_residentdocs_created_by_id_cd1099d9_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `towers_residentdocs_resident_id_0eb0f5c6_fk_towers_resident_id` FOREIGN KEY (`resident_id`) REFERENCES `towers_resident` (`id`),
  CONSTRAINT `towers_residentdocs_updated_by_id_f9901b3a_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `towers_residentdocs` */

/*Table structure for table `towers_tower` */

DROP TABLE IF EXISTS `towers_tower`;

CREATE TABLE `towers_tower` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `tower_name` varchar(255) NOT NULL,
  `tower_number` int(11) DEFAULT NULL,
  `description` longtext DEFAULT NULL,
  `photo` varchar(100) DEFAULT NULL,
  `num_floors` int(11) NOT NULL,
  `num_units` int(11) NOT NULL,
  `unit_naming_type` varchar(255) NOT NULL,
  `add_tower_number_to_unit_name` tinyint(1) NOT NULL,
  `units_per_floor` varchar(255) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `towers_tower_created_by_id_a4b67101_fk_user_member_id` (`created_by_id`),
  KEY `towers_tower_updated_by_id_d3b6180a_fk_user_member_id` (`updated_by_id`),
  CONSTRAINT `towers_tower_created_by_id_a4b67101_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `towers_tower_updated_by_id_d3b6180a_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `towers_tower` */

/*Table structure for table `towers_unit` */

DROP TABLE IF EXISTS `towers_unit`;

CREATE TABLE `towers_unit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `unit_name` varchar(20) NOT NULL,
  `unit_status` varchar(255) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `area` int(11) DEFAULT NULL,
  `number_of_rooms` int(11) DEFAULT NULL,
  `number_of_bathrooms` int(11) DEFAULT NULL,
  `number_of_balconies` int(11) DEFAULT NULL,
  `primary_name` varchar(255) DEFAULT NULL,
  `primary_number` varchar(11) DEFAULT NULL,
  `primary_email` varchar(254) DEFAULT NULL,
  `primary_relationship` varchar(50) DEFAULT NULL,
  `secondary_name` varchar(255) DEFAULT NULL,
  `secondary_number` varchar(11) DEFAULT NULL,
  `secondary_email` varchar(254) DEFAULT NULL,
  `secondary_relationship` varchar(50) DEFAULT NULL,
  `emergency_name` varchar(255) DEFAULT NULL,
  `emergency_number` varchar(11) DEFAULT NULL,
  `emergency_email` varchar(254) DEFAULT NULL,
  `emergency_relationship` varchar(50) DEFAULT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `floor_id` bigint(20) NOT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `towers_unit_created_by_id_03745fe5_fk_user_member_id` (`created_by_id`),
  KEY `towers_unit_floor_id_e9f2f473_fk_towers_floor_id` (`floor_id`),
  KEY `towers_unit_updated_by_id_66a6884a_fk_user_member_id` (`updated_by_id`),
  CONSTRAINT `towers_unit_created_by_id_03745fe5_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `towers_unit_floor_id_e9f2f473_fk_towers_floor_id` FOREIGN KEY (`floor_id`) REFERENCES `towers_floor` (`id`),
  CONSTRAINT `towers_unit_updated_by_id_66a6884a_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `towers_unit` */

/*Table structure for table `towers_unitdocs` */

DROP TABLE IF EXISTS `towers_unitdocs`;

CREATE TABLE `towers_unitdocs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `unit_docs` varchar(100) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `unit_id` bigint(20) NOT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `towers_unitdocs_created_by_id_fffbe77a_fk_user_member_id` (`created_by_id`),
  KEY `towers_unitdocs_unit_id_f8323b53_fk_towers_unit_id` (`unit_id`),
  KEY `towers_unitdocs_updated_by_id_0165e62b_fk_user_member_id` (`updated_by_id`),
  CONSTRAINT `towers_unitdocs_created_by_id_fffbe77a_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `towers_unitdocs_unit_id_f8323b53_fk_towers_unit_id` FOREIGN KEY (`unit_id`) REFERENCES `towers_unit` (`id`),
  CONSTRAINT `towers_unitdocs_updated_by_id_0165e62b_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `towers_unitdocs` */

/*Table structure for table `towers_unitstaff` */

DROP TABLE IF EXISTS `towers_unitstaff`;

CREATE TABLE `towers_unitstaff` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `is_active` tinyint(1) NOT NULL,
  `unit_staff_status` tinyint(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `member_id` bigint(20) NOT NULL,
  `unit_id` bigint(20) NOT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `towers_unitstaff_created_by_id_994ea559_fk_user_member_id` (`created_by_id`),
  KEY `towers_unitstaff_member_id_ca3cd9f5_fk_user_member_id` (`member_id`),
  KEY `towers_unitstaff_unit_id_c6624d91_fk_towers_unit_id` (`unit_id`),
  KEY `towers_unitstaff_updated_by_id_ab466530_fk_user_member_id` (`updated_by_id`),
  CONSTRAINT `towers_unitstaff_created_by_id_994ea559_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `towers_unitstaff_member_id_ca3cd9f5_fk_user_member_id` FOREIGN KEY (`member_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `towers_unitstaff_unit_id_c6624d91_fk_towers_unit_id` FOREIGN KEY (`unit_id`) REFERENCES `towers_unit` (`id`),
  CONSTRAINT `towers_unitstaff_updated_by_id_ab466530_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `towers_unitstaff` */

/*Table structure for table `user_company` */

DROP TABLE IF EXISTS `user_company`;

CREATE TABLE `user_company` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `company_name` varchar(255) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `unit_id` bigint(20) DEFAULT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `member_id` bigint(20) DEFAULT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `company_name` (`company_name`),
  KEY `user_company_unit_id_b459dc19_fk_towers_unit_id` (`unit_id`),
  KEY `user_company_created_by_id_7fae8cd3_fk_user_member_id` (`created_by_id`),
  KEY `user_company_member_id_3bf9a794_fk_user_member_id` (`member_id`),
  KEY `user_company_updated_by_id_5c2bdc7d_fk_user_member_id` (`updated_by_id`),
  CONSTRAINT `user_company_created_by_id_7fae8cd3_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `user_company_member_id_3bf9a794_fk_user_member_id` FOREIGN KEY (`member_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `user_company_unit_id_b459dc19_fk_towers_unit_id` FOREIGN KEY (`unit_id`) REFERENCES `towers_unit` (`id`),
  CONSTRAINT `user_company_updated_by_id_5c2bdc7d_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `user_company` */

/*Table structure for table `user_member` */

DROP TABLE IF EXISTS `user_member`;

CREATE TABLE `user_member` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `full_name` varchar(255) NOT NULL,
  `general_contact` varchar(11) NOT NULL,
  `general_email` varchar(254) NOT NULL,
  `login_email` varchar(254) DEFAULT NULL,
  `login_contact` varchar(11) DEFAULT NULL,
  `nid_number` varchar(255) DEFAULT NULL,
  `photo` varchar(100) DEFAULT NULL,
  `photo_low_quality` varchar(100) DEFAULT NULL,
  `about_us` longtext DEFAULT NULL,
  `facebook_profile` longtext DEFAULT NULL,
  `linkedin_profile` longtext DEFAULT NULL,
  `permanent_address` longtext DEFAULT NULL,
  `present_address` longtext DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `occupation` varchar(255) DEFAULT NULL,
  `gender` varchar(10) DEFAULT NULL,
  `marital_status` varchar(10) DEFAULT NULL,
  `religion` varchar(20) DEFAULT NULL,
  `nid_front` varchar(100) DEFAULT NULL,
  `nid_back` varchar(100) DEFAULT NULL,
  `is_org_member` tinyint(1) NOT NULL,
  `is_comm_member` tinyint(1) NOT NULL,
  `org_member_ever_created` tinyint(1) NOT NULL,
  `comm_member_ever_created` tinyint(1) NOT NULL,
  `is_first_login` tinyint(1) NOT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `created_by_id` bigint(20) DEFAULT NULL,
  `updated_by_id` bigint(20) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `member_type_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `login_email` (`login_email`),
  UNIQUE KEY `login_contact` (`login_contact`),
  UNIQUE KEY `nid_number` (`nid_number`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `user_member_created_by_id_6b01656a_fk_user_member_id` (`created_by_id`),
  KEY `user_member_updated_by_id_a2337e8c_fk_user_member_id` (`updated_by_id`),
  KEY `user_member_member_type_id_588cff55_fk_user_membertype_id` (`member_type_id`),
  CONSTRAINT `user_member_created_by_id_6b01656a_fk_user_member_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `user_member_member_type_id_588cff55_fk_user_membertype_id` FOREIGN KEY (`member_type_id`) REFERENCES `user_membertype` (`id`),
  CONSTRAINT `user_member_updated_by_id_a2337e8c_fk_user_member_id` FOREIGN KEY (`updated_by_id`) REFERENCES `user_member` (`id`),
  CONSTRAINT `user_member_user_id_a994aed0_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `user_member` */

insert  into `user_member`(`id`,`full_name`,`general_contact`,`general_email`,`login_email`,`login_contact`,`nid_number`,`photo`,`photo_low_quality`,`about_us`,`facebook_profile`,`linkedin_profile`,`permanent_address`,`present_address`,`date_of_birth`,`occupation`,`gender`,`marital_status`,`religion`,`nid_front`,`nid_back`,`is_org_member`,`is_comm_member`,`org_member_ever_created`,`comm_member_ever_created`,`is_first_login`,`created_at`,`updated_at`,`created_by_id`,`updated_by_id`,`user_id`,`member_type_id`) values 
(6,'Md. Tausif Hossain','01748181448','<EMAIL>','<EMAIL>',NULL,'2857046243','','','Technical Leader','https://www.facebook.com/tausif1337','https://linkedin.com/in/tausif1337','53/A, South Sastapur, Upazila Road, Fatullah, Narayanganj 1420','53/A, South Sastapur, Upazila Road, Fatullah, Narayanganj 1420','1996-04-12','Software Engineer','Male','Married','Islam','','',1,1,1,1,0,'2025-03-16 11:32:12.250063','2025-05-22 12:10:03.337025',NULL,6,6,1);

/*Table structure for table `user_membertype` */

DROP TABLE IF EXISTS `user_membertype`;

CREATE TABLE `user_membertype` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type_name` varchar(128) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `user_membertype` */

insert  into `user_membertype`(`id`,`type_name`) values 
(1,'Staff');

/*Table structure for table `user_mymodel` */

DROP TABLE IF EXISTS `user_mymodel`;

CREATE TABLE `user_mymodel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `age` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

/*Data for the table `user_mymodel` */

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
