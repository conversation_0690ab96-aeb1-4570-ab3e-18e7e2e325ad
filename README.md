# Project Documentation: Estate-Link Frontend

## Overview

This document outlines the dependencies and their usage for the **Estate-Link Frontend** project. It covers both regular and development dependencies, along with their installation commands and purposes.

---
## Dependencies

### Regular Dependencies

- **@reduxjs/toolkit**
  - **Version**: ^2.5.0
  - **Purpose**: Simplifies state management and reduces boilerplate in Redux.
  - **Installation Command**: 
    ```bash
    npm install @reduxjs/toolkit
    ```

- **axios**
  - **Version**: ^1.7.9
  - **Purpose**: Makes HTTP requests to interact with backend APIs.
  - **Installation Command**: 
    ```bash
    npm install axios
    ```

- **react**
  - **Version**: ^18.3.1
  - **Purpose**: A library for building user interfaces.
  - **Installation Command**: 
    ```bash
    npm install react
    ```

- **react-dom**
  - **Version**: ^18.3.1
  - **Purpose**: Provides DOM-specific methods for React.
  - **Installation Command**: 
    ```bash
    npm install react-dom
    ```

- **react-icons**
  - **Version**: ^5.4.0
  - **Purpose**: Includes popular icon packs for React applications.
  - **Installation Command**: 
    ```bash
    npm install react-icons
    ```

- **react-redux**
  - **Version**: ^9.2.0
  - **Purpose**: Official bindings for React to interact with Redux.
  - **Installation Command**: 
    ```bash
    npm install react-redux
    ```

- **react-router-dom**
  - **Version**: ^7.1.1
  - **Purpose**: Enables routing in React applications.
  - **Installation Command**: 
    ```bash
    npm install react-router-dom
    ```

---

### Development Dependencies

- **@eslint/js**
  - **Version**: ^9.17.0
  - **Purpose**: Core ESLint library for linting JavaScript code.
  - **Installation Command**: 
    ```bash
    npm install @eslint/js --save-dev
    ```

- **@types/react**
  - **Version**: ^18.3.18
  - **Purpose**: TypeScript definitions for React.
  - **Installation Command**: 
    ```bash
    npm install @types/react --save-dev
    ```

- **@types/react-dom**
  - **Version**: ^18.3.5
  - **Purpose**: TypeScript definitions for React DOM.
  - **Installation Command**: 
    ```bash
    npm install @types/react-dom --save-dev
    ```

- **@vitejs/plugin-react**
  - **Version**: ^4.3.4
  - **Purpose**: Official Vite plugin for React support.
  - **Installation Command**: 
    ```bash
    npm install @vitejs/plugin-react --save-dev
    ```

- **autoprefixer**
  - **Version**: ^10.4.20
  - **Purpose**: Adds vendor prefixes to CSS for cross-browser compatibility.
  - **Installation Command**: 
    ```bash
    npm install autoprefixer --save-dev
    ```

- **eslint**
  - **Version**: ^9.17.0
  - **Purpose**: A tool for identifying and fixing JavaScript code issues.
  - **Installation Command**: 
    ```bash
    npm install eslint --save-dev
    ```

- **eslint-plugin-react**
  - **Version**: ^7.37.2
  - **Purpose**: ESLint rules for React.
  - **Installation Command**: 
    ```bash
    npm install eslint-plugin-react --save-dev
    ```

- **eslint-plugin-react-hooks**
  - **Version**: ^5.0.0
  - **Purpose**: Enforces rules for React hooks.
  - **Installation Command**: 
    ```bash
    npm install eslint-plugin-react-hooks --save-dev
    ```

- **eslint-plugin-react-refresh**
  - **Version**: ^0.4.16
  - **Purpose**: ESLint plugin for React fast refresh.
  - **Installation Command**: 
    ```bash
    npm install eslint-plugin-react-refresh --save-dev
    ```

- **globals**
  - **Version**: ^15.14.0
  - **Purpose**: Provides a list of global variables.
  - **Installation Command**: 
    ```bash
    npm install globals --save-dev
    ```

- **postcss**
  - **Version**: ^8.4.49
  - **Purpose**: A tool for transforming CSS with JavaScript plugins.
  - **Installation Command**: 
    ```bash
    npm install postcss --save-dev
    ```

- **tailwindcss**
  - **Version**: ^3.4.17
  - **Purpose**: A utility-first CSS framework.
  - **Installation Command**: 
    ```bash
    npm install tailwindcss --save-dev
    ```

- **vite**
  - **Version**: ^6.0.5
  - **Purpose**: A fast development server and build tool.
  - **Installation Command**: 
    ```bash
    npm install vite --save-dev
    ```

---

## Scripts

- **dev**
  - **Command**: `vite`
  - **Purpose**: Starts the development server.

- **build**
  - **Command**: `vite build`
  - **Purpose**: Builds the project for production.

- **lint**
  - **Command**: `eslint .`
  - **Purpose**: Runs ESLint on the project files.

- **preview**
  - **Command**: `vite preview`
  - **Purpose**: Previews the production build.

---
